import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import DOMPurify from 'dompurify';
import { ChangeEvent, useEffect, useRef, useState } from 'react';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useLocation } from 'react-router-dom';
import { Badge } from '../../@/components/ui/badge';
import { Button } from '../../@/components/ui/Common/Elements/Button/ButtonSort';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '../../@/components/ui/Common/Elements/Select/Select';
import { Tabs, TabsList, TabsTrigger } from '../../@/components/ui/Common/Elements/Tabs/Tabs';
import httpClient from '../../api/httpClient';
import logoDark from '../../assets/GoTrustLogo.svg';
import userlogsIcon from '../../assets/images/userlogsIcon.svg';
import {
  convertDateToHumanView,
  convertString,
} from '../../components/common/CommonHelperFunctions';
import ErrorPage from '../../components/common/ErrorPage';
import { get_supported_languages } from '../../components/common/services/universal-consent-management';
import PrivacyNoticeReaderView from '../../components/UniversalConsentMangement/Common/privacy-notice-reader-view';
import { ConsentFlow } from '../../components/UniversalConsentMangement/SubjectConsentManagement/Lists/ListView/consent-flow';
import UserInputScreen from '../../components/UniversalConsentMangement/UCMLabs/ConsentBuilder/ConsentBuilderSteps/PreferenceCenter/UserInput/user-input';
import VerifyInputScreen from '../../components/UniversalConsentMangement/UCMLabs/ConsentBuilder/ConsentBuilderSteps/PreferenceCenter/VerifyInput/verify-input';
import {
  AuditLogData,
  CTCPMapBucket,
  PreferenceFormCenterConfiguration,
  PreferenceFormData,
  PreferenceFormFrequencyProperties,
  PrivacySection,
  SubjectIdentityDetailsProperties,
  UcmPreferenceForm,
  UserInputFormConfiguration,
  VerifyInputFormConfiguration,
} from '../../types/universal-consent-management';
import styles from './index.module.css';

const PreferenceForm = () => {
  const { t } = useTranslation();
  //! Variables
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const [tabData, setTabData] = useState([
    'Privacy Notice',
    'Consent Preference',
    'Data Subject Rights',
    'Consent Flow',
  ]);
  const EMAIL_PATTERN = new RegExp(
    '^.*(email|e-mail|mail|EMAIL ADDRESS).*$',
    'i' // i flag for case-insensitive matching
  );
  const PHONE_PATTERN = new RegExp('^.*(phone|mobile|contact|telephone).*$', 'i');
  // ! STATES
  const [customerId, setCustomerId] = useState<string>(queryParams?.get('customer_id') ?? '');
  const [pfId, setPfId] = useState<string>(queryParams?.get('pf_id') ?? '');
  const [otp, setOtp] = useState(new Array(6).fill(''));
  const [showVerificationModal, setShowVerificationModal] = useState<boolean>(false);
  const [showEmailModal, setShowEmailModal] = useState<boolean>(true);
  const [userInput, setUserInput] = useState<string>();
  const [subjectIdentityDetails, setSubjectIdentityDetails] =
    useState<SubjectIdentityDetailsProperties>();
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('0');
  const [preferenceFormData, setPreferenceFormData] = useState<PreferenceFormData>();
  const [userAuthenticated, setUserAuthenticated] = useState<boolean>(false);
  const [selectedSection, setSelectedSection] = useState<string | null>(null);
  const [sections, setSections] = useState<PrivacySection[] | null>();
  const [selectedSectionIndex, setSelectedSectionIndex] = useState<number | null>(null);
  const [consentStatus, setConsentStatus] = useState<Map<string, CTCPMapBucket[]>>(new Map());
  const [frequencyList, setFrequencyList] = useState<PreferenceFormFrequencyProperties[]>([]);
  const [ip, setIP] = useState('');
  const [countryCode, setCountryCode] = useState<string>('');
  const [continent, setContinent] = useState<string>('');
  const [PreferenceFormCenterConfiguration, setPreferenceCenterConfiguration] =
    useState<PreferenceFormCenterConfiguration>();
  // New state to track selected frequency per consent
  const [selectedLanguage, setSelectedLanguage] = useState<string>('en');
  const [languages, setLanguages] = useState([]);
  const [showAuditLog, setShowAuditLog] = useState(false);
  const [lastItemHeight, setLastItemHeight] = useState(80);
  const [userInputFormConfiguration, setUserInputFormConfiguration] =
    useState<UserInputFormConfiguration>();

  const [verifyInputFormConfiguration, setVerifyInputFormConfiguration] =
    useState<VerifyInputFormConfiguration>();

  const lastItemRef = useRef<HTMLDivElement | null>(null);

  let fontSize = '';
  const badgeClass = 'text-[white] bg-primary';
  //! LOGS
  //! EFFECTS

  // fetching ip and country code
  useEffect(() => {
    const getData = async () => {
      const response = await axios.get('https://api.ipify.org/?format=json');
      setIP(response.data.ip);
    };

    const getCountry = async () => {
      const response = await axios.get(
        'https://api.ipdata.co?api-key=295ad47616f5cc86434a4a501a2b9dba7eb6aeec63a70e09620aab10'
      );
      setCountryCode(response?.data?.country_code);
      setContinent(response?.data?.continent_name);
    };

    getData();
    getCountry();
  }, []);

  useEffect(() => {
    if (!customerId || !pfId) {
      // Set an error message if customerId or pfId is missing
      setError('Please enter correct URL');
      return; // Stop execution if there's an error
    }

    const getData = async () => {
      try {
        const response = await axios.get(
          `${import.meta.env.VITE_APP_GO_TRUST_UNIVERSAL_BASE_API}/api/v1/pc/identity/ask?customer_id=${customerId}&preference_form_id=${pfId}&language_code=${selectedLanguage}`
        );
        if (response?.status === 200) {
          setSubjectIdentityDetails(response?.data?.result?.data[0]);
          setUserInputFormConfiguration(
            response?.data?.result?.data[0]?.preference_center_configuration
              ?.user_input_configuration
          );
          setVerifyInputFormConfiguration(
            response?.data?.result?.data[0]?.preference_center_configuration
              ?.verify_input_configuration
          );
        }
      } catch (error) {
        console.error('Error fetching subject identity details:', error);
        setError('Error fetching subject identity details.');
      }
    };

    getData();
  }, [customerId, pfId, selectedLanguage]);

  useEffect(() => {
    const getData = async () => {
      try {
        const response = await axios.get(
          `${import.meta.env.VITE_APP_GO_TRUST_UNIVERSAL_BASE_API}/api/v3/pc/display?customer_id=${customerId}&preference_center_id=${pfId}&subject_identity_value=${userInput}&language_code=${selectedLanguage}`
        );
        if (response?.status === 200) {
          const formData: PreferenceFormData = response?.data?.result?.data;
          setPreferenceFormData(formData);
          setSections(formData?.privacy_note);
          setPreferenceCenterConfiguration(formData?.preference_center_configuration);
          setTabData([
            formData?.preference_center_configuration?.privacy_notice_heading ?? 'Privacy Notice',
            ...(formData?.preference_center_configuration?.showConsent
              ? [
                  formData?.preference_center_configuration?.preference_center_heading ??
                    'Consent Preference',
                ]
              : []),
            ...(formData?.dsr?.need_dsr_center
              ? [
                  formData?.preference_center_configuration?.dsr_center_heading ??
                    'Data Subject Rights',
                ]
              : []),
            ...(formData?.preference_center_configuration?.showCookie ? ['Cookie Preference'] : []),
            ...(formData?.preference_center_configuration?.showConsentFlow
              ? [formData?.preference_center_configuration?.consent_flow_heading ?? 'Consent Flow']
              : []),
          ]);

          // Populate consentStatus state based on response data
          formData?.ucm_preference_form?.length > 0 &&
            formData.ucm_preference_form.forEach((formItem) => {
              formItem?.purpose_list?.forEach((purpose) => {
                purpose.consent_bucket.forEach((consent) => {
                  setConsentStatus((prev) => {
                    const updatedMap = new Map(prev);
                    updatedMap.set(
                      `${consent.consent_purpose_id}_${formItem?.collection_template_id}`,
                      consent?.ct_cp_map_bucket
                    );
                    return updatedMap;
                  });
                });
              });
            });
        }
      } catch (error) {
        console.error('Error fetching subject identity details:', error);
      }
    };

    if (customerId && pfId && userInput && userAuthenticated) getData();
  }, [customerId, pfId, userInput, userAuthenticated, selectedLanguage]);

  // Check if 'pf_token' exists in localStorage
  useEffect(() => {
    const token = localStorage.getItem('pf_token');
    if (token) {
      const userDetailsString = localStorage.getItem('pf_user_details');
      if (userDetailsString) {
        const userDetails = JSON.parse(userDetailsString);
        setCustomerId(userDetails.customer_id);
        setPfId(userDetails.pf_id);
        setUserInput(userDetails.user_input);
      }
      setShowEmailModal(false);
      setShowVerificationModal(false);
    }
  }, []);

  // fetching frequency
  useEffect(() => {
    const fetchFrequency = async () => {
      try {
        // Call the returned function to fetch data
        const response = await axios.get(
          `${import.meta.env.VITE_APP_GO_TRUST_UNIVERSAL_BASE_API}/api/v2/ct/frequency-list?language_code=${selectedLanguage}`
        );
        setFrequencyList(response?.data?.result?.data);
      } catch (error) {
        console.error(error);
      }
    };

    fetchFrequency();
  }, [customerId, pfId, selectedLanguage]);

  // Clear localStorage when the tab or window is closed
  useEffect(() => {
    const handleBeforeUnload = () => {
      localStorage.removeItem('pf_token');
      localStorage.removeItem('pf_user_details');
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // Clean up the event listener on component unmount
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  // Set first section as default if sections are available
  useEffect(() => {
    if (preferenceFormData?.privacy_note && preferenceFormData.privacy_note.length > 0) {
      setSelectedSectionIndex(0);
    }
  }, [preferenceFormData?.privacy_note]);

  // fetching Supported Languages
  useEffect(() => {
    const fetchSupportedLanguages = async () => {
      try {
        // Call the returned function to fetch data
        const responseData = await get_supported_languages(
          'preference_center',
          Number(customerId),
          undefined,
          Number(pfId),
          undefined
        );
        setLanguages(responseData?.result?.data);
      } catch (error) {
        console.error(error);
      }
    };

    fetchSupportedLanguages();
  }, [customerId, pfId]);

  //! Logs

  //! Handler Functions
  // Function to handle frequency change
  const handleFrequencyChange = (uniqueId: string, newFrequency: string) => {
    // setConsentFrequency((prev) => ({
    //   ...prev,
    //   [consentId]: newFrequency,
    // }));

    // Also update the frequency in consentStatus
    setConsentStatus((prev) => {
      // Clone the existing Map (React state requires immutability)
      const updatedMap = new Map(prev);
      let updatedValue: CTCPMapBucket[] | undefined = updatedMap?.get(uniqueId);

      if (updatedValue) {
        updatedValue = updatedValue.map((item) => ({
          ...item,
          frequency: newFrequency?.toLowerCase(),
        }));

        updatedMap.set(uniqueId, updatedValue);
      }

      return updatedMap; // Update state with new Map
    });
  };

  const handleChange = (element: HTMLInputElement, index: number) => {
    if (isNaN(Number(element.value))) return;

    setOtp([...otp.map((d, idx) => (idx === index ? element.value : d))]);

    // Move to the next input if the current input has a value
    const nextSibling = element.nextElementSibling as HTMLInputElement | null;
    if (element.value && nextSibling) {
      nextSibling.focus();
    }
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>, index: number) => {
    if (event.key === 'Backspace' && !otp[index] && index > 0) {
      setOtp([...otp.map((d, idx) => (idx === index - 1 ? '' : d))]);

      // Move to the previous input if it exists
      const previousSibling = event.currentTarget.previousElementSibling as HTMLInputElement | null;
      if (previousSibling) {
        previousSibling.focus();
      }
    }
  };

  const handleSubmitUserInput = async () => {
    toast.dismiss();

    // Define regex for email and phone number
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^\d{10}$/; // assuming 10-digit phone numbers

    if (!userInput) {
      toast.error(`Please input your ${subjectIdentityDetails?.subject_identity_type_name}`);
      return;
    }

    // Provide a fallback value for subject_identity_type_name
    const identityType = subjectIdentityDetails?.subject_identity_type_name || '';

    // Check if the userInput matches the specified input type
    // if (EMAIL_PATTERN.test(identityType)) {
    //   if (!emailRegex.test(userInput)) {
    //     toast.error('Please enter a valid email address.');
    //     return;
    //   }
    // } else if (PHONE_PATTERN.test(identityType)) {
    //   if (!phoneRegex.test(userInput)) {
    //     toast.error('Please enter a valid 10-digit phone number.');
    //     return;
    //   }
    // } else {
    //   toast.error('Invalid input type specified.');
    //   return;
    // }

    // Create payload with validated input
    const payload = {
      customer_id: customerId,
      email_address: userInput,
    };

    try {
      const response = await axios.post(
        `${import.meta.env.VITE_APP_GO_TRUST_UNIVERSAL_BASE_API}/api/v1/pc/otp/send`,
        payload
      );
      if (response?.data?.result?.data?.user_found) {
        toast.success(t('ToastMessages.Authentication.OTPSentSuccessfully'));
        setShowEmailModal(false);
        setShowVerificationModal(true);
      } else {
        toast.error(t('FrontEndErrorMessage.FormValidation.PleaseProvideYourConsentFirst'));
        return;
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error(t('FrontEndErrorMessage.Authentication.FailedToSendOTP'));
    }
  };

  const handleSubmit = async () => {
    toast.dismiss();

    // Join the OTP array into a single string
    const otpCode = otp.join('');

    // Check if OTP is provided and is exactly 6 digits
    if (!otpCode || otpCode.length !== 6) {
      toast.error(t('FrontEndErrorMessage.FormValidation.PleaseEnterValidOTP'));
      return;
    }

    const payload = {
      email_address: userInput,
      otp: otpCode,
    };

    try {
      const response = await axios.post(
        `${import.meta.env.VITE_APP_GO_TRUST_UNIVERSAL_BASE_API}/api/v1/pc/otp/verify`,
        payload
      );
      if (response.status === 200) {
        localStorage.setItem('pf_token', response?.data?.result?.data[0]?.token);
        localStorage.setItem(
          'pf_user_details',
          JSON.stringify({
            customer_id: customerId,
            pf_id: pfId,
            user_input: userInput,
          })
        );
        setUserAuthenticated(true);
        setShowVerificationModal(false);
        toast.success(t('ToastMessages.Authentication.OTPVerifiedSuccessfully'));
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      toast.error('Invalid OTP');
    }
  };

  const handleResendOtp = async () => {
    toast.dismiss();
    const payload = {
      customer_id: customerId,
      email_address: userInput,
    };

    try {
      const response = await axios.post(
        `${import.meta.env.VITE_APP_GO_TRUST_UNIVERSAL_BASE_API}/api/v1/pc/otp/send`,
        payload
      );
      toast.success(t('ToastMessages.Authentication.OTPSentSuccessfully'));
      setOtp(new Array(6).fill(''));
    } catch (error) {
      console.error('Error resending OTP:', error);
      toast.error(t('FrontEndErrorMessage.Authentication.FailedToSendOTP'));
    }
  };

  const handleSectionSelect = (index: number) => {
    setSelectedSectionIndex(index);
  };

  const handleConsentChange = (uniqueId: string, checked: boolean) => {
    setConsentStatus((prev) => {
      // Clone the existing Map (React state requires immutability)
      const updatedMap = new Map(prev);
      let updatedValue: CTCPMapBucket[] | undefined = updatedMap?.get(uniqueId);

      if (updatedValue) {
        updatedValue = updatedValue.map((item) => ({
          ...item,
          consent_status: checked,
        }));

        updatedMap.set(uniqueId, updatedValue);
      }
      // Update existing entry OR add a new one

      return updatedMap; // Update state with new Map
    });
  };
  const handleConsentChangeAll = (collection_template_id: number, checked: boolean) => {
    setConsentStatus((prev) => {
      const updatedMap = new Map<string, CTCPMapBucket[]>();
      // For every consent purpose (key) in your Map, update the consent_status for each bucket entry.
      prev.forEach((consentList, consentId) => {
        const updatedConsentList = consentList.map((item) => {
          if (!item.compulsory_consent && item.collection_template_id === collection_template_id) {
            return {
              ...item,
              consent_status: checked,
            };
          } else {
            return item;
          }
        });
        updatedMap.set(consentId, updatedConsentList);
      });
      return updatedMap;
    });
  };
  function formatDateToDDMMYYYY(dateString: string) {
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();

    return `${day}/${month}/${year}`;
  }

  const handleChangePreferences = async () => {
    if (!preferenceFormData) {
      toast.error(t('FrontEndErrorMessage.FormValidation.PreferenceDataNotAvailable'));
      return;
    }

    // Check if all required consent checkboxes are checked
    const allRequiredChecked = Array.from(consentStatus.values()).every((consentList) =>
      consentList.every((consent) =>
        consent.compulsory_consent ? consent.consent_status === true : true
      )
    );

    if (!allRequiredChecked) {
      toast.dismiss();
      toast.error(t('FrontEndErrorMessage.FormValidation.PleaseAgreeToAllRequiredConsents'));
      return;
    }

    // Create the full payload
    const payload = {
      customer_id: preferenceFormData?.additional_info?.customer_id,
      verification_key: preferenceFormData?.additional_info?.verification_key,
      subject_identity_id: preferenceFormData?.additional_info?.subject_identity_type_id,
      subject_identity_value: userInput,
      consent_source: 'web_preference_center',
      geolocation: `${ip}[${countryCode}]`,
      continent,
      consent_status: Array.from(consentStatus.values()).flat(),
    };

    try {
      const response = await axios.post(
        `${import.meta.env.VITE_APP_GO_TRUST_UNIVERSAL_BASE_API}/api/v2/data-collector/pc-records`,
        payload,
        {
          headers: {
            accept: 'application/json',
            'Content-Type': 'application/json',
          },
        }
      );
      toast.success(t('ToastMessages.Consent.PreferencesChangedSuccessfully'));
    } catch (error) {
      console.error('Error changing preferences:', error);
      toast.error(t('FrontEndErrorMessage.FormValidation.FailedToChangePreferences'));
    }
  };

  const renderSectionContent = (section: PrivacySection) => {
    if (!section) return <div>Section Not Available</div>;

    return (
      <div className="mb-8 text-sm">
        <h2 className="mb-2 font-semibold">{section.title}</h2>
        <p className="mb-2 text-gray-700">{section.content}</p>
      </div>
    );
  };

  // Conditionally render ErrorPage if there's an error
  if (error) {
    return <ErrorPage errorMessage={error} showFooter={false} />;
  }

  const extractDataPrincipalIds = (ucmPreferenceForm: UcmPreferenceForm[]) => {
    const ids: number[] = [];

    ucmPreferenceForm.forEach((formItem) => {
      formItem.purpose_list?.forEach((purpose: any) => {
        purpose.consent_bucket?.forEach((bucket: any) => {
          const firstMap = bucket.ct_cp_map_bucket?.[0];
          const id = firstMap?.data_principal_id;
          if (id !== undefined && id !== null) {
            ids.push(id);
          }
        });
      });
    });

    const uniqueIds = Array.from(new Set(ids));
    return uniqueIds.join(',');
  };

  const fetchAuditLogData = async (customer_id: string) => {
    const dataPrincipalIds = extractDataPrincipalIds(preferenceFormData?.ucm_preference_form ?? []);
    try {
      const response = await httpClient.get(
        `${import.meta.env.VITE_APP_GO_TRUST_UNIVERSAL_BASE_API}/api/v2/subject-consent-manager/audit-logs`,
        {
          params: {
            customer_id,
            data_principal_ids: dataPrincipalIds,
          },
        }
      );
      if (response?.data?.status_code !== 200) {
        throw new Error('Failed to fetch data');
      }
      const result = response?.data?.result?.data;
      return result;
    } catch (error) {
      console.error('Error fetching audit log data:', error);
    }
  };

  const { data: auditLogData } = useQuery<AuditLogData[]>({
    queryKey: ['auditLogData', customerId, preferenceFormData, showAuditLog],
    queryFn: () => fetchAuditLogData(customerId),
    enabled: !!customerId && !!preferenceFormData,
    initialData: [],
    refetchOnWindowFocus: false,
  });

  useEffect(() => {
    if (lastItemRef.current) {
      lastItemRef.current.scrollIntoView({ behavior: 'smooth', block: 'end' });
    }
  }, [auditLogData]);

  const renderBoldText = (text: string) => {
    const parts = text.split(/(\*\*[^\*]+\*\*)/);
    return parts.map((part, index) => {
      if (part.startsWith('**') && part.endsWith('**')) {
        return <strong key={index}>{part.slice(2, -2)}</strong>;
      } else {
        return <span key={index}>{part}</span>;
      }
    });
  };

  useEffect(() => {
    // if (activeTab !== '1') {
    setShowAuditLog(false);
    // }
  }, [activeTab]);

  function RenderAuditLog() {
    return (
      <div className="p-6">
        <button
          onClick={() => setShowAuditLog(false)}
          className="mb-4 text-blue-600 underline hover:text-blue-800"
        >
          {/* ← Back to Preferences */}← Back to {tabData[Number(activeTab)]}
        </button>
        <div className="relative">
          {/* Continuous vertical line */}
          {auditLogData.length > 0 && (
            <div
              className="absolute left-4 top-5 w-0.5 bg-ucm-blue"
              style={{
                height: auditLogData.length > 1 ? `calc(100% - ${lastItemHeight}px)` : '0',
              }}
            />
          )}

          {/* Audit data items with icons overlaying the line */}
          <div className="flex flex-col">
            {auditLogData.map((item, index) => (
              <div
                className="flex"
                key={item.id}
                ref={index === auditLogData.length - 1 ? lastItemRef : null}
              >
                {/* Icon column - positioned to overlay the continuous line */}
                <div className="relative">
                  <div className="relative top-5 rounded-full bg-primary-background">
                    <img src={userlogsIcon} alt="userLogsIcon" className="size-8" />
                  </div>
                </div>

                {/* Data column */}
                <div className="mb-20 ml-4 flex-1">
                  <p className="w-full rounded-md border border-primary-border bg-white p-5">
                    {renderBoldText(item.action_description) ?? '-'}
                  </p>
                  <p className="mt-1 text-xs font-medium">
                    {convertDateToHumanView(item?.change_time ?? '') ?? '-'}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      {showEmailModal && (
        // <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 font-primary-text">
        //   <div className="mx-auto flex w-[430px] max-w-md flex-col gap-2 rounded-lg bg-white p-8 shadow-lg">
        //     <Label className="mb-4 text-2xl font-semibold">{`Enter your ${subjectIdentityDetails?.subject_identity_type_name}`}</Label>
        //     {/* <p className="mb-6 text-center text-gray-600">
        //       We have sent a verification <NAME_EMAIL>
        //     </p> */}

        //     <Input
        //       className="w-full"
        //       value={userInput}
        //       onChange={(e) => setUserInput(e.target.value)}
        //     />

        //     <Button onClick={handleSubmitUserInput} className="w-full text-primary-background">
        //       Next
        //     </Button>
        //   </div>
        // </div>
        <div className="flex h-screen w-screen">
          <UserInputScreen
            formConfig={userInputFormConfiguration}
            handleSubmitUserInput={handleSubmitUserInput}
            setUserInput={setUserInput}
            subject_identity={subjectIdentityDetails?.subject_identity_type_name}
            selectedLanguage={selectedLanguage}
            setSelectedLanguage={setSelectedLanguage}
            languages={languages}
          />
        </div>
      )}
      {showVerificationModal && (
        // <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 font-primary-text">
        //   <div className="mx-auto w-full max-w-md rounded-lg bg-white p-8 shadow-lg">
        //     <h2 className="mb-4 text-center text-2xl font-semibold">Verify your email</h2>
        //     <p className="mb-6 text-center">
        //       We have sent a verification code to <strong>{`${userInput}`}</strong>
        //     </p>

        //     <div className="mb-6 flex justify-center gap-2">
        //       {otp.map((data, index) => (
        //         <input
        //           key={index}
        //           type="text"
        //           maxLength={1}
        //           value={data}
        //           onChange={(e) => handleChange(e.target, index)}
        //           onKeyDown={(e) => handleKeyDown(e, index)}
        //           className="flex h-12 w-12 rounded border border-solid border-gray-300 bg-background px-3 py-2 text-center text-sm font-medium ring-offset-background placeholder:text-gray-400 focus:outline-none focus:ring-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
        //         />
        //       ))}
        //     </div>

        //     <Button onClick={handleSubmit} className="w-full text-primary-background">
        //       Verify
        //     </Button>
        //     <div className="mt-3 flex w-full items-center justify-center">
        //       <button
        //         className="text-xs text-blue-600 hover:cursor-pointer hover:underline"
        //         onClick={handleResendOtp}
        //       >
        //         Resend OTP
        //       </button>
        //     </div>
        //   </div>
        // </div>
        <div className="flex h-screen w-screen">
          <VerifyInputScreen
            formConfig={verifyInputFormConfiguration}
            handleSubmit={handleSubmit}
            handleResendOtp={handleResendOtp}
            handleChange={handleChange}
            handleKeyDown={handleKeyDown}
            otp={otp}
            setOtp={setOtp}
            subject_identity_value={userInput ?? ''}
            selectedLanguage={selectedLanguage}
            setSelectedLanguage={setSelectedLanguage}
            languages={languages}
          />
        </div>
      )}
      {!showVerificationModal && !showEmailModal && (
        <main
          className="flex h-screen w-screen flex-col gap-5 p-6"
          style={{ fontFamily: PreferenceFormCenterConfiguration?.fontFamily ?? 'poppins' }}
        >
          <div className="flex w-full flex-col gap-2">
            <header className="flex h-fit w-full flex-row items-center justify-between gap-4">
              {PreferenceFormCenterConfiguration?.showLogo && (
                <img
                  src={PreferenceFormCenterConfiguration?.logoUrl ?? logoDark}
                  alt="logo"
                  style={{
                    width:
                      PreferenceFormCenterConfiguration?.consent_purpose_configuration?.form?.logo
                        ?.width,
                    height:
                      PreferenceFormCenterConfiguration?.consent_purpose_configuration?.form?.logo
                        ?.height,
                    objectFit: 'contain',
                  }}
                />
              )}
              <div
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  width: 'fit-content',
                  flexDirection: 'row',
                  gap: '10px',
                }}
              >
                {' '}
                <select
                  value={selectedLanguage}
                  onChange={(event: React.ChangeEvent<HTMLSelectElement>) =>
                    setSelectedLanguage(event.target.value)
                  }
                  className="h-fit w-fit rounded-lg bg-gray-300 p-2"
                >
                  <option value="en" key="en">
                    English
                  </option>
                  {languages?.map((lang: { language_code: string; language: string }) => (
                    <option value={lang.language_code} key={lang.language_code}>
                      {lang.language}
                    </option>
                  ))}
                </select>
                <button className={styles.closeButton}>X</button>
              </div>
            </header>
            <div className="flex w-full flex-row items-center justify-between gap-2">
              <p className="h-full max-w-[80%] text-2xl font-semibold">
                {PreferenceFormCenterConfiguration?.title}
              </p>
            </div>

            <p className="">{PreferenceFormCenterConfiguration?.description}</p>
          </div>

          <div className="flex w-full justify-between">
            <Tabs
              value={activeTab}
              onValueChange={(currentTab) => setActiveTab(currentTab)}
              className="h-auto w-1/3 min-w-[600px] flex-wrap items-center rounded-lg bg-[#f6f6f6] p-1"
              style={{
                borderRadius:
                  PreferenceFormCenterConfiguration?.consent_purpose_configuration?.form
                    ?.border_radius,
              }}
            >
              <TabsList className="flex size-full flex-row items-center justify-between">
                {tabData?.map((item, index) => (
                  <Button variant="ghost" className="w-full p-0">
                    <TabsTrigger
                      value={index.toString()}
                      className={`${activeTab === index.toString() ? 'text-white' : ''} w-full`}
                      style={{
                        backgroundColor:
                          activeTab === index.toString()
                            ? `${PreferenceFormCenterConfiguration?.consent_purpose_configuration?.form?.color_scheme}`
                            : 'transparent',
                        color: activeTab === index.toString() ? `white` : 'gray',
                        borderRadius:
                          PreferenceFormCenterConfiguration?.consent_purpose_configuration?.form
                            ?.border_radius,
                      }}
                    >
                      {item}
                    </TabsTrigger>
                  </Button>
                ))}
              </TabsList>
            </Tabs>
            {/* {activeTab === '1' && ( */}
            <div className="pt-3">
              <button
                onClick={() => setShowAuditLog(true)}
                className="text-blue-600 underline hover:text-blue-800"
              >
                View Logs
              </button>
            </div>
            {/* )}  */}
          </div>
          {activeTab === '0' && (
            <div className="flex h-fit w-full justify-end gap-3">
              {' '}
              <p className="text-sm font-semibold">
                Privacy Notice Version: {preferenceFormData?.basic_info?.privacy_note_version}
                <p>
                  Last updated on :{' '}
                  {formatDateToDDMMYYYY(
                    preferenceFormData?.additional_info?.updated_at
                      ? preferenceFormData?.additional_info.updated_at
                      : ''
                  )}
                </p>
              </p>
            </div>
          )}

          <section
            className={`w-full overflow-auto ${styles.table_main_content}`}
            style={{ height: 'calc(100vh - 300px)' }}
          >
            {activeTab === '0' && (
              // <div className="flex h-full w-full flex-col md:flex-row">
              //   <div
              //     className={`h-1/3 w-full overflow-auto border-r border-gray-300 p-4 md:h-full md:w-1/4 ${styles.table_main_content}`}
              //   >
              //     <h3 className="mb-4 font-medium">Sections</h3>
              //     <div className="flex flex-col space-y-2 text-base">
              //       {preferenceFormData?.privacy_note.map((section, index) => (
              //         <div key={index} className="flex items-center">
              //           <button
              //             onClick={() => handleSectionSelect(index)}
              //             className={`w-full border-b border-gray-200 p-2 text-left text-sm transition hover:bg-gray-100 ${
              //               selectedSectionIndex === index ? 'bg-gray-200 font-semibold' : ''
              //             }`}
              //           >
              //             {section.title}
              //           </button>
              //         </div>
              //       ))}
              //     </div>
              //   </div>

              //   <div
              //     className={`h-2/3 w-full overflow-auto p-4 md:h-full md:w-3/4 ${styles.table_main_content} text-sm`}
              //   >
              //     {selectedSectionIndex !== null &&
              //       preferenceFormData?.privacy_note[selectedSectionIndex] &&
              //       renderSectionContent(preferenceFormData.privacy_note[selectedSectionIndex])}
              //   </div>
              // </div>
              <>
                {!showAuditLog ? (
                  <PrivacyNoticeReaderView sections={preferenceFormData?.privacy_note} />
                ) : (
                  <RenderAuditLog />
                )}
              </>
            )}
            {activeTab === '1' && (
              <div className="flex h-full w-full flex-col gap-5">
                {!showAuditLog ? (
                  <>
                    {preferenceFormData?.ucm_preference_form?.map((item) => (
                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          gap: '12px',
                          width: '100%',
                        }}
                        key={item?.collection_template_id}
                      >
                        <p style={{ fontSize: '18px' }}>
                          {convertString(item?.collection_template_name)}
                        </p>
                        <div
                          style={{
                            display: 'flex',
                            flexDirection: 'column',
                            gap: '8px',
                            width: '100%',
                          }}
                        >
                          {PreferenceFormCenterConfiguration?.consent_purpose_configuration
                            ?.consent_collection_section?.heading?.text && (
                            <p
                              style={{
                                fontWeight: 'bold',
                                fontSize:
                                  PreferenceFormCenterConfiguration?.consent_purpose_configuration
                                    ?.consent_collection_section?.heading?.size,
                                fontFamily:
                                  PreferenceFormCenterConfiguration?.consent_purpose_configuration
                                    ?.form?.font_family,
                                color:
                                  PreferenceFormCenterConfiguration?.consent_purpose_configuration
                                    ?.consent_collection_section?.heading?.color,
                              }}
                            >
                              {
                                PreferenceFormCenterConfiguration?.consent_purpose_configuration
                                  ?.consent_collection_section?.heading?.text
                              }
                            </p>
                          )}
                          {PreferenceFormCenterConfiguration?.consent_purpose_configuration
                            ?.consent_collection_section?.description?.text && (
                            <p
                              style={{
                                fontSize:
                                  PreferenceFormCenterConfiguration?.consent_purpose_configuration
                                    ?.consent_collection_section?.description?.size,
                                fontFamily:
                                  PreferenceFormCenterConfiguration?.consent_purpose_configuration
                                    ?.form?.font_family,
                                color:
                                  PreferenceFormCenterConfiguration?.consent_purpose_configuration
                                    ?.consent_collection_section?.description?.color,
                              }}
                            >
                              {
                                PreferenceFormCenterConfiguration?.consent_purpose_configuration
                                  ?.consent_collection_section?.description?.text
                              }
                            </p>
                          )}
                          {PreferenceFormCenterConfiguration?.consent_purpose_configuration
                            ?.consent_collection_section?.show_check_all_checkbox ? (
                            <div>
                              <div
                                style={{
                                  display: 'flex',
                                  flexDirection: 'row',
                                  alignItems: 'start',
                                  gap: '8px',
                                }}
                              >
                                <input
                                  type="checkbox"
                                  style={{
                                    marginTop: '4px',
                                    height: '18px',
                                    width: '18px',
                                    cursor: 'pointer',
                                    accentColor:
                                      PreferenceFormCenterConfiguration
                                        ?.consent_purpose_configuration?.form?.color_scheme,
                                    borderRadius:
                                      PreferenceFormCenterConfiguration
                                        ?.consent_purpose_configuration?.form?.border_radius,
                                  }}
                                  defaultChecked={
                                    consentStatus?.get(
                                      `${item?.purpose_list[0]?.consent_bucket[0]?.consent_purpose_id}_${item?.collection_template_id}`
                                    )?.[0]?.consent_status
                                  }
                                  checked={
                                    consentStatus?.get(
                                      `${item?.purpose_list[0]?.consent_bucket[0]?.consent_purpose_id}_${item?.collection_template_id}`
                                    )?.[0]?.consent_status
                                  }
                                  onChange={(event: ChangeEvent<HTMLInputElement>) =>
                                    handleConsentChangeAll(
                                      item?.collection_template_id,
                                      event.target.checked
                                    )
                                  }
                                  id={item?.collection_template_id?.toString()}
                                />
                                <p
                                  style={{
                                    fontFamily:
                                      PreferenceFormCenterConfiguration
                                        ?.consent_purpose_configuration?.form?.font_family,
                                    fontSize:
                                      PreferenceFormCenterConfiguration
                                        ?.consent_purpose_configuration?.consent_collection_section
                                        ?.description?.size,
                                  }}
                                >
                                  {
                                    PreferenceFormCenterConfiguration?.consent_purpose_configuration
                                      ?.consent_collection_section?.all_checkbox_text
                                  }
                                </p>
                              </div>
                              <ul style={{ listStyleType: 'disc', paddingLeft: '2.5rem' }}>
                                {item?.purpose_list?.map((purpose) => (
                                  <>
                                    {/* Nested list for consent buckets */}
                                    {purpose.consent_bucket.map((consent) => (
                                      <li
                                        key={consent.consent_purpose_id}
                                        style={{ marginBottom: '1rem' }}
                                      >
                                        {/* Wrap your flex layout in a child div */}
                                        <div
                                          style={{
                                            display: 'flex',
                                            flexDirection: 'column',
                                            gap: '8px',
                                          }}
                                        >
                                          <p style={{ margin: 0 }}>
                                            <strong>{consent.consent_purpose_name}</strong>
                                          </p>
                                          <p style={{ margin: 0 }}>
                                            {consent.consent_purpose_description}
                                          </p>
                                          {/* PII Labels */}
                                          {consent?.pii_labels?.length > 0 &&
                                            PreferenceFormCenterConfiguration
                                              ?.consent_purpose_configuration?.pii_section
                                              ?.show_badges && (
                                              <div
                                                style={{
                                                  display: 'flex',
                                                  flexDirection: 'row',
                                                  flexWrap: 'wrap',
                                                  gap: '8px',
                                                }}
                                              >
                                                <span>PII:</span>
                                                {consent.pii_labels.map((label, index) =>
                                                  label ? (
                                                    <Badge
                                                      key={index}
                                                      style={{
                                                        color: 'white',
                                                        backgroundColor:
                                                          PreferenceFormCenterConfiguration
                                                            ?.consent_purpose_configuration?.form
                                                            ?.color_scheme,
                                                      }}
                                                    >
                                                      {label}
                                                    </Badge>
                                                  ) : null
                                                )}
                                              </div>
                                            )}
                                          {/* Vendors */}
                                          {consent?.vendors?.length > 0 &&
                                            PreferenceFormCenterConfiguration
                                              ?.consent_purpose_configuration?.pii_section
                                              ?.show_badges && (
                                              <div
                                                style={{
                                                  display: 'flex',
                                                  flexDirection: 'row',
                                                  flexWrap: 'wrap',
                                                  gap: '8px',
                                                }}
                                              >
                                                <span>Vendor:</span>
                                                {consent.vendors.map((vendor, index) =>
                                                  vendor ? (
                                                    <Badge
                                                      key={index}
                                                      style={{
                                                        color: 'white',
                                                        backgroundColor:
                                                          PreferenceFormCenterConfiguration
                                                            ?.consent_purpose_configuration?.form
                                                            ?.color_scheme,
                                                      }}
                                                    >
                                                      {vendor}
                                                    </Badge>
                                                  ) : null
                                                )}
                                              </div>
                                            )}
                                          {/* Processes */}
                                          {consent?.processes?.length > 0 &&
                                            PreferenceFormCenterConfiguration
                                              ?.consent_purpose_configuration?.pii_section
                                              ?.show_badges && (
                                              <div
                                                style={{
                                                  display: 'flex',
                                                  flexDirection: 'row',
                                                  flexWrap: 'wrap',
                                                  gap: '8px',
                                                }}
                                              >
                                                <span>Process:</span>
                                                {consent.processes.map((process, index) =>
                                                  process ? (
                                                    <Badge
                                                      key={index}
                                                      style={{
                                                        color: 'white',
                                                        backgroundColor:
                                                          PreferenceFormCenterConfiguration
                                                            ?.consent_purpose_configuration?.form
                                                            ?.color_scheme,
                                                      }}
                                                    >
                                                      {process}
                                                    </Badge>
                                                  ) : null
                                                )}
                                              </div>
                                            )}
                                        </div>
                                      </li>
                                    ))}
                                  </>
                                ))}
                              </ul>
                            </div>
                          ) : (
                            <>
                              {item?.purpose_list?.map((purpose) => (
                                <div key={purpose.processing_purpose_id}>
                                  {purpose?.processing_purpose_name && (
                                    <p
                                      style={{
                                        fontSize: '16px',
                                        fontWeight: 'semibold',
                                        color: '#000000',
                                      }}
                                    >
                                      {purpose.processing_purpose_name}
                                    </p>
                                  )}
                                  {purpose.consent_bucket.map((consent) => (
                                    <div
                                      key={consent.consent_purpose_id}
                                      style={{
                                        marginTop: '8px',
                                        display: 'grid',
                                        width: '100%',
                                        alignItems: 'start',
                                        gap: '16px',
                                        gridTemplateColumns: consent.ct_cp_map_bucket[0]?.frequency
                                          ? '4fr 1fr'
                                          : '1fr',
                                      }}
                                    >
                                      <div
                                        style={{
                                          display: 'flex',
                                          flexDirection: 'row',
                                          width: '100%',
                                          alignItems: 'flex-start',
                                          gap: '8px',
                                        }}
                                      >
                                        <div
                                          style={{
                                            display: 'flex',
                                            alignItems: 'center',
                                          }}
                                        >
                                          <input
                                            style={{
                                              height: '16px',
                                              width: '16px',
                                              marginTop: '3px',
                                              cursor:
                                                consent.ct_cp_map_bucket[0]
                                                  ?.default_opt_out_allowed &&
                                                consent.ct_cp_map_bucket[0]?.compulsory_consent
                                                  ? 'not-allowed'
                                                  : 'pointer',
                                              accentColor:
                                                PreferenceFormCenterConfiguration
                                                  ?.consent_purpose_configuration?.form
                                                  ?.color_scheme,
                                              borderRadius:
                                                PreferenceFormCenterConfiguration
                                                  ?.consent_purpose_configuration?.form
                                                  ?.border_radius,
                                            }}
                                            defaultChecked={
                                              consentStatus?.get(
                                                `${consent?.consent_purpose_id}_${item?.collection_template_id}`
                                              )?.[0]?.consent_status
                                                ? true
                                                : consent.ct_cp_map_bucket[0]
                                                      ?.default_opt_out_allowed
                                                  ? true
                                                  : false
                                            }
                                            type="checkbox"
                                            checked={
                                              consentStatus?.get(
                                                `${consent?.consent_purpose_id}_${item?.collection_template_id}`
                                              )?.[0]?.consent_status
                                            }
                                            onChange={(event: ChangeEvent<HTMLInputElement>) =>
                                              handleConsentChange(
                                                `${consent?.consent_purpose_id}_${item?.collection_template_id}`,
                                                event.target.checked
                                              )
                                            }
                                            required={
                                              consent.ct_cp_map_bucket[0]?.compulsory_consent
                                            }
                                            disabled={
                                              consent.ct_cp_map_bucket[0]
                                                ?.default_opt_out_allowed &&
                                              consent.ct_cp_map_bucket[0]?.compulsory_consent
                                            }
                                            id={consent?.consent_purpose_id?.toString()}
                                          />
                                          <label
                                            htmlFor={consent?.consent_purpose_id?.toString()}
                                            className="text-sm text-gray-700"
                                          >
                                            {consent?.ct_cp_map_bucket[0]?.compulsory_consent && (
                                              <span className="ml-1 text-red-500">*</span>
                                            )}
                                          </label>
                                        </div>

                                        <div className="flex flex-col flex-wrap gap-2">
                                          <p className="flex flex-col gap-2">
                                            <p
                                              style={{
                                                fontSize: '16px',
                                                fontWeight: 'semibold',
                                                color: '#000000',
                                              }}
                                            >
                                              {consent.consent_purpose_name}
                                            </p>
                                            <p>{consent.consent_purpose_description}</p>
                                          </p>
                                          {/* PII Labels */}
                                          {consent?.pii_labels?.length > 0 &&
                                            PreferenceFormCenterConfiguration
                                              ?.consent_purpose_configuration?.pii_section
                                              ?.show_badges && (
                                              <p className="flex w-full flex-row flex-wrap gap-2">
                                                <p>PII:</p>
                                                {consent?.pii_labels?.map((consent) => {
                                                  if (consent) {
                                                    return (
                                                      <Badge
                                                        style={{
                                                          color: 'white',
                                                          backgroundColor:
                                                            PreferenceFormCenterConfiguration
                                                              ?.consent_purpose_configuration?.form
                                                              ?.color_scheme,
                                                        }}
                                                      >
                                                        {consent}
                                                      </Badge>
                                                    );
                                                  }
                                                  return <></>;
                                                })}
                                              </p>
                                            )}
                                          {/* Vendors */}
                                          {consent?.vendors?.length > 0 &&
                                            PreferenceFormCenterConfiguration
                                              ?.consent_purpose_configuration?.pii_section
                                              ?.show_badges && (
                                              <p className="flex w-full flex-row flex-wrap gap-2">
                                                <p>Vendor:</p>
                                                {consent?.vendors?.map((consent) => {
                                                  if (consent) {
                                                    return (
                                                      <Badge
                                                        style={{
                                                          color: 'white',
                                                          backgroundColor:
                                                            PreferenceFormCenterConfiguration
                                                              ?.consent_purpose_configuration?.form
                                                              ?.color_scheme,
                                                        }}
                                                      >
                                                        {consent}
                                                      </Badge>
                                                    );
                                                  }
                                                  return <></>;
                                                })}
                                              </p>
                                            )}
                                          {/* Processes */}
                                          {consent?.processes?.length > 0 &&
                                            PreferenceFormCenterConfiguration
                                              ?.consent_purpose_configuration?.pii_section
                                              ?.show_badges && (
                                              <p className="flex w-full flex-row flex-wrap gap-2">
                                                <p>Process:</p>
                                                {consent?.processes?.map((consent) => {
                                                  if (consent) {
                                                    return (
                                                      <Badge
                                                        style={{
                                                          color: 'white',
                                                          backgroundColor:
                                                            PreferenceFormCenterConfiguration
                                                              ?.consent_purpose_configuration?.form
                                                              ?.color_scheme,
                                                        }}
                                                      >
                                                        {consent}
                                                      </Badge>
                                                    );
                                                  }
                                                  return <></>;
                                                })}
                                              </p>
                                            )}
                                        </div>
                                      </div>

                                      {consent.ct_cp_map_bucket[0]?.frequency && (
                                        <div
                                          style={{
                                            display: 'flex',
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            gap: '8px',
                                            width: '100%',
                                            paddingRight: '10px',
                                          }}
                                        >
                                          <label
                                            style={{
                                              width: 'auto',
                                            }}
                                          >
                                            Frequency:
                                          </label>
                                          <div style={{ width: '100%' }}>
                                            <Select
                                              defaultValue={consent.ct_cp_map_bucket[0].frequency}
                                            >
                                              <SelectTrigger
                                                style={{
                                                  width: '100%',
                                                  fontFamily:
                                                    PreferenceFormCenterConfiguration
                                                      ?.consent_purpose_configuration?.form
                                                      ?.font_family,
                                                }}
                                              >
                                                <SelectValue placeholder="Select Frequency" />
                                              </SelectTrigger>
                                              <SelectContent>
                                                <SelectGroup>
                                                  <SelectLabel>Frequency</SelectLabel>
                                                  {frequencyList.map((option) => (
                                                    <SelectItem value={option.key} key={option.key}>
                                                      {option.name}
                                                    </SelectItem>
                                                  ))}
                                                </SelectGroup>
                                              </SelectContent>
                                            </Select>
                                          </div>
                                        </div>
                                      )}
                                    </div>
                                  ))}
                                </div>
                              ))}
                            </>
                          )}
                        </div>
                      </div>
                    ))}
                    {PreferenceFormCenterConfiguration?.consent_purpose_configuration?.form_footer
                      ?.heading?.text && (
                      <p
                        style={{
                          marginTop: '20px',
                          width: '100%',
                          fontSize:
                            PreferenceFormCenterConfiguration?.consent_purpose_configuration
                              ?.form_footer?.heading?.size,
                          fontFamily:
                            PreferenceFormCenterConfiguration?.consent_purpose_configuration?.form
                              ?.font_family,
                          color:
                            PreferenceFormCenterConfiguration?.consent_purpose_configuration
                              ?.form_footer?.heading?.color,
                          textAlign: 'justify',
                        }}
                      >
                        {
                          PreferenceFormCenterConfiguration?.consent_purpose_configuration
                            ?.form_footer?.heading?.text
                        }
                      </p>
                    )}

                    {PreferenceFormCenterConfiguration?.consent_purpose_configuration?.form_footer
                      ?.description?.text && (
                      <p
                        style={{
                          marginTop: '20px',
                          width: '100%',
                          fontSize:
                            PreferenceFormCenterConfiguration?.consent_purpose_configuration
                              ?.form_footer?.description?.size,
                          fontFamily:
                            PreferenceFormCenterConfiguration?.consent_purpose_configuration?.form
                              ?.font_family,
                          color:
                            PreferenceFormCenterConfiguration?.consent_purpose_configuration
                              ?.form_footer?.description?.color,
                          textAlign: 'justify',
                        }}
                      >
                        {
                          PreferenceFormCenterConfiguration?.consent_purpose_configuration
                            ?.form_footer?.description?.text
                        }
                      </p>
                    )}
                    <footer className="flex w-full justify-end">
                      <button
                        style={{
                          fontFamily:
                            PreferenceFormCenterConfiguration?.consent_purpose_configuration?.form
                              ?.font_family,
                          fontSize:
                            PreferenceFormCenterConfiguration?.consent_purpose_configuration
                              ?.submit_button?.size,
                          background:
                            PreferenceFormCenterConfiguration?.consent_purpose_configuration
                              ?.submit_button?.color,
                          color: 'white',
                          marginTop: '10px',
                          borderRadius:
                            PreferenceFormCenterConfiguration?.consent_purpose_configuration?.form
                              ?.border_radius,
                          padding: '10px 20px 10px 20px',
                          width: 'fit-content',
                          height: 'fit-content',
                          cursor: 'pointer',
                        }}
                        onClick={handleChangePreferences}
                      >
                        {
                          PreferenceFormCenterConfiguration?.consent_purpose_configuration
                            ?.submit_button?.text
                        }
                      </button>
                    </footer>
                  </>
                ) : (
                  <RenderAuditLog />
                )}
              </div>
            )}
            {activeTab === '2' && (
              <div className="flex w-full flex-col gap-4">
                {!showAuditLog ? (
                  <>
                    {preferenceFormData?.dsr?.dsr_content ? (
                      <p
                        dangerouslySetInnerHTML={{
                          __html: DOMPurify.sanitize(preferenceFormData?.dsr.dsr_content, {
                            ADD_ATTR: ['target', 'rel'],
                            ALLOWED_ATTR: ['href', 'target', 'rel', 'class', 'style'],
                          }),
                        }}
                      ></p>
                    ) : (
                      <>
                        <p>
                          If you wish to exercise your rights as a data subject in respect of the
                          above, you may contact GoTrust through the{' '}
                          <a
                            href={`${import.meta.env.VITE_APP_FRONTEND_URL}/data-subject-rights/task-overview/create-request`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-500 underline transition duration-200 ease-in-out hover:text-blue-700"
                          >
                            GoTrust’s dedicated portal
                          </a>
                          . For other questions about your account on the GoTrust Services (for
                          example, questions about the availability of the services and login
                          errors), please contact GoTrust, as applicable, at the address{' '}
                          <i><EMAIL></i>
                        </p>
                        <p>
                          Please only use this form for queries related to your personal data (Data
                          Subject request). For support, technical assistance and other types of
                          requests, please contact us through our{' '}
                          <a
                            href={`${import.meta.env.VITE_APP_FRONTEND_URL}/data-subject-rights/task-overview/create-request`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-500 transition duration-200 ease-in-out hover:text-blue-700"
                          >
                            contact form
                          </a>
                          . The information collected in this form is intended to enable the Data
                          Protection Officer to respond to your Data Subject Request. This
                          information will be kept and retained after the Data Subject Request has
                          been treated for a period of (01) one year and then deleted. In case your
                          request is related to deletion, please note that this deletion request
                          will apply to all data collected by our company and all related Organizing
                          Committees for our company's websites/applications where you have
                          registered.
                        </p>
                      </>
                    )}
                  </>
                ) : (
                  <RenderAuditLog />
                )}
              </div>
            )}
            {activeTab === '3' && (
              <div className="flex h-full w-full flex-col gap-4">
                <div className="my-5">
                  {!showAuditLog ? (
                    <ConsentFlow customer_id={customerId} subject_identity={userInput} />
                  ) : (
                    <RenderAuditLog />
                  )}
                </div>
              </div>
            )}
          </section>
        </main>
      )}
    </>
  );
};

export default PreferenceForm;
