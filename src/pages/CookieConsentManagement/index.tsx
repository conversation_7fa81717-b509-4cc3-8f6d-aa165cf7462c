import React from 'react';
import { useLocation } from 'react-router-dom';
import CustomHelmet from '../../@/components/ui/Common/Helmet/Helmet';
import { CookiePolicy } from '../../components/CookieConsentManagement/cookie-policy/CookiePolicy';

import { CookiePolicyEditor } from '../../components/CookieConsentManagement/cookie-policy/CookiePolicyEditor';
import { ViewCookiePolicy } from '../../components/CookieConsentManagement/cookie-policy/ViewCookiePolicy';
import CookieConfiguration from '../../components/CookieConsentManagement/CookieConsentDomain/cookie-configuration';
import CookieConsentDomain from '../../components/CookieConsentManagement/CookieConsentDomain/cookie-consent-domain';
import { CookieDictionary } from '../../components/CookieConsentManagement/CookieDictionary/cookie-dictionary';
import CookieConsentDashboard from '../../components/CookieConsentManagement/Dashboard/cookie-consent';
import { RecordedConsents } from '../../components/CookieConsentManagement/recorded-consents/RecordedConsents';
import ErrorBoundary from '../../components/ErrorBoundary/error-boundary';
import {
  COOKIE_CONFIGURATION,
  COOKIE_CONSENT_DOMAIN,
  COOKIE_CONSENT_MANAGEMENT,
  COOKIE_DICTIONARY,
  COOKIE_POLICY,
  COOKIE_POLICY_CREATE,
  COOKIE_POLICY_DETAILS,
  POLICY_MANAGEMENT_COOKIE_POLICY,
  RECORDED_CONSENTS,
  VIEW_COOKIE_POLICY,
} from '../../utils/routeConstant';

interface CookieConsentManagementProperties {
  pathName: string;
}

const CookieConsentManagement: React.FC<CookieConsentManagementProperties> = ({ pathName }) => {
  const location = useLocation();
  const path = location.pathname;

  const renderContent = () => {
    if (path === COOKIE_CONSENT_DOMAIN) return <CookieConsentDomain />;
    if (path === COOKIE_CONFIGURATION) return <CookieConfiguration />;
    if (path === COOKIE_CONSENT_MANAGEMENT) return <CookieConsentDashboard />;
    if (path === COOKIE_POLICY) return <CookiePolicy />;
    if (path === COOKIE_POLICY_CREATE) return <CookiePolicyEditor isCreateMode={true} />;
    if (path === RECORDED_CONSENTS) return <RecordedConsents />;
    if (path === COOKIE_DICTIONARY) return <CookieDictionary />;
    if (path === VIEW_COOKIE_POLICY) return <ViewCookiePolicy />;
    if (path === POLICY_MANAGEMENT_COOKIE_POLICY) return <CookiePolicy />;

    if (path === COOKIE_POLICY_DETAILS) return <CookiePolicyEditor isCreateMode={false} />;
    return null;
  };

  return (
    <ErrorBoundary>
      <div className="size-full px-5">
        <CustomHelmet title="GoTrust | Cookie Consent Management" />
        <main className="h-full">{renderContent()}</main>
      </div>
    </ErrorBoundary>
  );
};

export default CookieConsentManagement;
