import { ColumnDef } from '@tanstack/react-table';
import { Editor } from '@tiptap/react';
import { ReactNode } from 'react';
import { IconType } from '../components/common/Stepper/types';

export type WorkflowModule = 'DSR' | 'UCM';
// Constants
export const WORKFLOW_MODULES: { value: WorkflowModule; label: string }[] = [
  { value: 'DSR', label: 'Data Subject Rights' },
  { value: 'UCM', label: 'Universal Consent Management' },
];
export interface AssignDialogProperties {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  requestId: number | string;
  onAssignSuccess?: () => void;
  currentAssignee?: string;
  groupId: string;
}

export interface Assignee {
  id: number;
  firstName: string;
  lastName: string;
}

export interface EmailVerificationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onEmailVerified: (email: string) => void;
}
export interface OtpVerificationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onVerify: () => void;
  email: string;
  onBack: () => void;
}
export interface TableHeading {
  id: string;
  label: string;
  sort: boolean;
  width: string;
}

export type RequestDocument = {
  url: string;
  original_name: string;
  document_type: string;
};

export type ApprovedRequestTableData = {
  tableHeadings?: TableHeading[];
  isApproved: boolean;
  searchTerm: string;
};

export type ApprovedRowData = {
  workflow_step_id: number;
  id: number;
  dsr_id: string | undefined;
  subject_type: string;
  full_name: string | undefined;
  request_type: string | undefined;
  data_discovery: string | undefined;
  start_date: string | undefined;
  deadline_date: string | undefined;
  deadline: string;
  extended: string;
  status: string;
  steps_status: string;
  assignee: string;
  User: {
    firstName: string;
    lastName: string;
  };
  DataSubject: {
    country: {
      country_name: string;
    };
    relationship: string;
    first_name: string;
    last_name: string;
    email: string;
    phone_no: string;
    postal_code: number;
  };
  DsrRequestType: {
    id: number;
    flowtype: string;
  };
  RequestDocuments: RequestDocument[];
  steps: steps[];
  description: string;
  business_unit: string;
  reject_reason: string;
  has_unread_messages: boolean;
};
export interface steps {
  id: number;
}

export interface UploadedFile {
  id: string;
  file: File;
  original_name: string;
  size: number;
  type: string;
}
export interface User {
  firstName: string;
  lastName: string;
}

export interface ApprovedData {
  dataDetails: ApprovedRowData | undefined;
  details: any;
  handleTaskDataChangeStep: (activeStepIndex: number) => void;
}
export interface AssigneeTaskTable {
  title?: string;
  assigned_to?: string;
  created_date?: Date | string;
  end_date?: Date | string;
  department?: string;
  progress?: string;
  steps?: string;
  priority?: string;
  attachment?: number;
}
export interface AssigneeFormData {
  id?: number;
  stage_id: number | undefined;
  title: string;
  department_id: string;
  start_date: string | undefined;
  due_date: string | undefined;
  guidance_text: string;
  assignee_id: string[];
  TaskDocuments: UploadedFile[];
  documents?: [];
  request_id: number;
  requirement: string;
}

export interface ProgressUpdateData {
  progress: string;
  request_id: number;
}

export interface Department {
  id: number;
  name: string;
  parent_id: any;
  customer_id: number;
  spoc_id: number;
}

export interface AssigneeTaskTablesubjectData {
  id: number;
  first_name: string;
  last_name: string;
  relationship: string;
}
export interface AssigneeTaskTableuserData {
  id: number;
  firstName: string;
  lastName: string;
  relationship: string;
}
export type AssigneeTaskTableRowData = {
  id: string | number;
  dsr_id: string;
  subject_type: string;
  full_name: string;
  request_type: string;
  owner: string;
  deadline: string;
  createdAt: string;
  DataSubject: AssigneeTaskTablesubjectData;
  DsrRequestType: { flowtype: string };
  deadline_date: string;
  User: AssigneeTaskTableuserData;
  extended: string;
  business_unit: any;
};

export interface AssigneeTaskDataTableProps {
  searchTerm: string;
}

export interface TableHeading {
  id: string;
  label: string;
  sort: boolean;
  width: string;
}

export type CompletedRequestTableData = {
  tableHeadings?: TableHeading[];
  data?: CompleteRequestDataTableRowData[];
  loading?: boolean;
  searchTerm: string;
};

export type CompleteRequestDataTableRequestDocument = {
  url: string;
  original_name: string;
  document_type: string;
};

export type CompleteRequestDataTableRowData = {
  workflow_step_id: number;
  id: number;
  dsr_id: string | undefined;
  subject_type: string;
  full_name: string | undefined;
  request_type: string | undefined;
  data_discovery: string | undefined;
  start_date: string | undefined;
  deadline_date: string | undefined;
  deadline: string;
  extended: string;
  status: string;
  steps_status: string;
  assignee: string;
  User: {
    firstName: string;
    lastName: string;
  };
  DataSubject: {
    country: {
      country_name: string;
    };
    relationship: string;
    first_name: string;
    last_name: string;
    email: string;
    phone_no: string;
    postal_code: number;
  };
  DsrRequestType: {
    id: number;
    flowtype: string;
  };
  RequestDocuments: CompleteRequestDataTableRequestDocument[];
  steps: steps[];
  description: string;
  business_unit: string;
  reject_reason: string;
  has_unread_messages: boolean;
};

export type AssigneeViewTableRowData = {
  id: string | number;
  dsr_id: string;
  subject_type: string;
  full_name: string;
  request_type: string;
  owner: string;
  deadline: string;
  DataSubject: AssigneeViewTableSubjectData;
  DsrRequestType: { flowtype: string };
  deadline_date: string;
  start_date: string;
  createdAt: string;
  User: AssigneeViewTableUserData;
  status: string;
  extended: string;
  business_unit: any;
};
export interface AssigneeViewTableSubjectData {
  id: number;
  first_name: string;
  last_name: string;
  relationship: string;
}
export interface AssigneeViewTableUserData {
  id: number;
  firstName: string;
  lastName: string;
  relationship: string;
}
export interface DsrDataTableuser {
  id: number;
  firstName: string;
  lastName: string;
}

export interface PendingRequestTableHeading {
  id: string;
  label: string;
  sort: boolean;
  width: string;
}

export type PendingRequestRowData = {
  assignee: string;
  data_discovery: string | undefined;
  id: string | number;
  request_type: string;
  requested_by: string;
  request_date: string;
  region: string;
  User: {
    email: string;
    firstName: string;
    lastName: string;
  };
  DataSubject: {
    email: string;
    first_name: string;
    id: number;
    last_name: string;
    relationship: string;
  };
  dsr_id: string;
  workflow_step_id: number | string;
  first_verification: boolean;
  second_verification: boolean;
  business_unit: string;
  has_unread_messages: boolean;
};

export interface PendingRequestDataTableProps {
  searchTerm: string;
}

export interface RejectingInProgressTableHeading {
  id: string;
  label: string;
  sort: boolean;
  width: string;
}

export type RejectingInProgressTableData = {
  tableHeadings?: RejectingInProgressTableHeading[];
  data?: RejectingInProgressRowData[];
  loading?: boolean;
  searchTerm: string;
};

export type RejectingInProgressRowData = {
  assignee: string;
  id: string | number;
  User: {
    email: string;
    firstName: string;
    lastName: string;
  };
  data_discovery: string | undefined;
  request_type: string;
  requested_by: string;
  request_date: string;
  region: string;
  workflow_step_id: number | string;
  reason: string;
  business_unit: string;
  has_unread_messages: boolean;
};

export interface RejectedrequestTableHeading {
  id: string;
  label: string;
  sort: boolean;
  width: string;
}

export type RejectedrequestTableData = {
  tableHeadings?: RejectedrequestTableHeading[];
  data?: RejectedrequestRowData[];
  loading?: boolean;
  searchTerm: string;
};

export type RejectedrequestRowData = {
  assignee: string;
  id: string | number;
  data_discovery: string | undefined;
  request_type: string;
  requested_by: string;
  request_date: string;
  region: string;
  reason: string;
  has_unread_messages: boolean;
};

export interface DsrDashboardWorkFlowData {
  status: string;
  count: number;
}
export interface DsrDashboardresidencyChartData {
  continent: string;
  grant: number;
  declined: number;
  withdrawn: number;
}

export interface DsrDashboardHalfDoughnutChartData {
  label: string[];
  count: number[];
  backgroundColor: string[];
}
export interface DsrDashboardChartData {
  label: string[];
  count: number[];
}
export interface DsrDashboardStatData {
  title: string;
  value: number;
}

export interface DsrDashboardStageData {
  month: string;
  desktop: number;
}
export interface DsrDashboardRiskData {
  month: string;
  desktop: number;
  mobile: number;
}

export interface DsrDashboardRequestType {
  id: string;
  flowtype: string;
}

export interface DsrDashboardEntityProperties {
  id: number;
  name: string;
  parent_id: number | null;
  customer_id: number;
  user_id: number;
  spoc_id: number | null;
  status: string;
  createdAt: string; // ISO 8601 date string
  updatedAt: string; // ISO 8601 date string
  deletedAt: string | null; // ISO 8601 date string or null if not deleted
}

export interface CreateTemplateModalProperties {
  isOpen: boolean;
  onClose: () => void;
}

export interface CreateEmailTemplateFormProps {
  URL: string;
  headerColor: string;
  file: File | null;
  formBackgroundColor: string;
}

export interface CreateEmailTemplateData {
  [x: string]: any;
  logo: File | null;
  headerColor: string;
  subject: string;
  content: string;
}

export interface CreateRequestFormData {
  subject: string;
  editorContent: string;
}
export interface CreateRequestFormProps {
  onValidSubmit: (data: CreateRequestFormData) => void;
  initialData?: Partial<CreateRequestFormData>;
}

export interface EmailTemplateMenuBarProperties {
  editor: Editor | null;
  onContentChange: (content: string) => void;
}

export interface EmailTemplateButtonWrapperProperties {
  onClick: () => void;
  isActive: boolean;
  children: ReactNode;
}

export interface TemplateTableDataItem {
  id: string;
  name: string;
  modifiedDate: string;
  createdAt: string;
  action: string;
  subject: string;
  content: string;
  headerColor?: string;
  logoUrl?: string;
}

export interface TemplateTableProps {
  data: TemplateTableDataItem[];
  loading: boolean;
  columns: ColumnDef<TemplateTableDataItem>[];
}

export interface EmailTemplateUpdatePayload {
  subject?: string;
  content?: string;
  headerColor?: string;
  logoUrl?: string;
}
export interface EmailtemplateDataItem {
  id: string;
  name: string;
  modifiedDate: string;
  createdAt: string;
  action: string;
  subject: string;
  content: string;
  headerColor?: string;
  logoUrl?: string;
}

export interface AddTaskModalTaskOverViewProperties {
  handleSubmit: (data: AddTaskModalTaskOverViewFormData) => Promise<void>;
  addTaskModalOpen: boolean;
  setAddTaskModalOpen: () => void;
  handleUpdate?: () => void;
  departments: AddTaskModalTaskOverViewDepartment[];
  groupId: string;
}

export interface AddTaskModalTaskOverViewFormData {
  id: number;
  stage_id: number | undefined;
  title: string;
  department_id: string;
  start_date: string;
  due_date: string;
  guidance_text: string;
  assignee_id: string[];
  TaskDocuments: AddTaskModalTaskOverViewUploadedFile[];
  requirement: string;
  step: number;
}

export interface AddTaskModalTaskOverViewUploadedFile {
  id: string;
  file: File;
  original_name: string;
  size: number;
  type: string;
  url?: string;
}
export interface AddTaskModalTaskOverViewDepartment {
  id: number;
  name: string;
  parent_id: any;
  customer_id: number;
  spoc_id: number;
}

export interface AddTaskModalProperties {
  handleSubmit: (data: AddTaskModalFormData) => Promise<void>;
  addTaskModalOpen: boolean;
  setAddTaskModalOpen: () => void;
  isEditMode: boolean;
  isAutomationEnabled: boolean;
}
export interface AddTaskModalUploadedFile {
  id: string;
  file: File;
  original_name: string;
  size: number;
  type: string;
}
export interface AddTaskModalFormData {
  id: number;
  stage_id: number;
  title: string;
  guidance_text: string;
  assignee_id: string[];
  due_date: string;
  start_date: string;
  TaskDocuments: AddTaskModalUploadedFile[];
  requirement: string;
  workflow_id: string;
  due_days: number;
}
export interface AddWorkFlowTaskModalProperties {
  handleSubmit: (value: any) => void;
  isOpen: boolean;
  setAddTaskModalOpen: (value: boolean) => void;
}
export interface CancelModalProperties {
  isOpen?: boolean;
  setIsOpen: (value: boolean) => void;
  handleSubmit?: () => void;
}

export interface DocumentUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpload: (files: File[]) => Promise<void>;
  onComplete: () => void;
}
export interface CompletionModalsProps {
  isDocumentSharedOpen: boolean;
  setIsDocumentSharedOpen: (value: boolean) => void;
  isConfirmCompleteOpen: boolean;
  setIsConfirmCompleteOpen: (value: boolean) => void;
  onComplete: () => void;
  isLoading?: boolean;
}
export interface ConfirmModalProperties {
  type: string;
  isOpen?: boolean;
  setIsOpen: (value: boolean) => void;
  handleSubmit?: () => void;
  submitAsDraft: () => void;
}
export interface IconWithModalProps {
  data?: string | null;
  status?: string;
}
export interface SuccessProperties {
  onClose: () => void;
}
export interface UpdateTaskModalUser {
  id: number;
  firstName: string;
  lastName: string;
}

export interface UpdateTaskModalTaskData {
  id: number;
  title?: string;
  assigned_to?: [];
  created_date?: Date | string;
  end_date?: Date | string;
  department?: string;
  progress?: string;
  steps?: string;
  priority?: string;
  attachment?: number;
  start_date?: string | undefined;
  due_date?: string | undefined;
  guidance_text?: string;
  TaskDocuments?: UpdateTaskModalUploadedFile[];
  users?: [];
  department_id: string;
  assignee_id: string[];
  Department?: {
    name: string;
  };
  RequestTypeStage?: {
    step_title: string;
  };
  documents: [];
  requirement: string;
  due_days: number;
}
export interface UpdateTaskModalTaskViewProperties {
  showTaskView: boolean;
  setShowTaskView: (value: boolean) => void;
  data: UpdateTaskModalTaskData;
  setAddTaskModalOpen: () => void;
  handleUpdate: (data: UpdateTaskModalFormData) => void;
  // assignee_id:string[];
  setSelectAssignee: React.Dispatch<React.SetStateAction<UpdateTaskModalUser[]>>;
  isTaskOpen?: boolean;
}

export interface UpdateTaskModalFormData {
  id: number;
  stage_id: number;
  title: string;
  department_id: string;
  // start_date?: string | Date;
  // due_date?: string | Date;
  guidance_text: string;
  assignee_id: string[];
  TaskDocuments: UpdateTaskModalUploadedFile[];
  requirement: string;
  workflow_id: string;
  due_days: number;
}
export interface UpdateTaskModalUploadedFile {
  id: string;
  file: File;
  original_name: string;
  size: number;
  type: string;
  url?: string;
}

export interface FileUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUploadSuccess: (data: any) => void;
}

export type ActionDropdownProps = {
  step_title: string;
  id: number | string;
  onRename?: (newTitle: string) => void;
  onMoveUp?: () => void;
  onMoveDown?: () => void;
  onDelete?: () => void;
};

export interface AddWorkFlowDataTableDataItem {
  id: number;
  title: string;
  assignee_id: string[];
  created_date: Date | string;
  end_date: Date | string;
  department: string;
  priority: string;
  attachment: number;
  activepieces_automation_id: string | null;
  guidance_text: string;
  due_days: number;
}
export interface AddWorkFlowDataTableFormData {
  id: number;
  stage_id: number;
  title: string;
  guidance_text: string;
  activepieces_automation_id?: string | null;
  workflow_id: string;
  due_days: number;
}

export interface AddWorkflowDataTableProperties {
  data: AddWorkFlowDataTableDataItem[];
  columns: ColumnDef<AddWorkFlowDataTableDataItem>[];
  loading: boolean;
  handleUpdate: (data: AddWorkFlowDataTableFormData) => void;
  showTaskView: boolean;
  setShowTaskView: (value: boolean) => void;
  userData: AddWorkFlowDataTableUserDetails[];
  isTaskOpen?: boolean;
  isEditMode: boolean;
}

export interface AddWorkFlowDataTableUserDetails {
  id: number;
  firstName: string;
  lastName: string;
}
export interface AddWorkFlowDataTableUserDetailsUser {
  id: number;
  firstName: string;
  lastName: string;
}
export interface WorkflowList {
  // allWorkflow:{
  flowtype: string;
  created_by: string;
  created_date: Date | string;
  workflow: string;
  // }
}
export interface WorkflowInterface {
  id: number;
  flowtype: string;
  module_name: string;
  created_by: string;
  created_date: Date | string;
  workflow: string;
  group_id: string;
  CommonWorkflowSteps: WorkflowStep[];
}
export interface WorkflowStep {
  id: string;
  label: string;
  step_title: string;
  description: string;
  activepieces_automation_id: string;
}
export interface WorkflowStepSubmit {
  step_title: string;
  guidance_text: string;
  type_id: number;
}
export interface DataItem {
  flowtype: string;
  created_by: string;
  created_date: Date | string;
  workflow: string;
}

export interface ApiError extends Error {
  response?: {
    status: number;
    data: any;
  };
}

export interface DsrEditWorkflowStepperProperties {
  isEditMode?: boolean;
  workFlow?: string;
}
export interface DsrEditWorkflowActionsProperties {
  isEditMode?: boolean;
  stepperData: WorkflowInterface;
  isActualLastStep: boolean;
}

export interface DsrWorkflowTableItem {
  id: number;
  flowtype: string;
  createdAt: string;
  firstName: string;
  lastName: string;
  workflow_status: string;
  group_id: string;
  Group: {
    id: number;
    name: string;
  };
  module_name: string;
}
export interface DsrWorkflowTableProp {
  flowtype: string;
  group_id: string;
  module_name: string;
  // regulation_id?: number[];
}

export interface WorkflowDataTableProperties {
  data: DsrWorkflowTableItem[];
  columns: ColumnDef<DsrWorkflowTableProp>[];
  loading: boolean;
}

export interface EditableCheckboxProps {
  initialTitle: string;
  initialChecked?: boolean;
  onCheckChange?: (title: string, checked: boolean) => void;
}
export interface LeftNewFormBuilderProps {
  onLogoChange: (logoUrl: string) => void;
  onHeaderDescriptionChange: (description: string) => void;
  initialLogoUrl?: string;
  initialHeaderDescription?: string;
  activeTab: 'basic' | 'elements';
  setActiveTab: (tab: 'basic' | 'elements') => void;
}

export interface RightNewFormBuilderProps {
  selectedElement: FormElement | null;
  onElementUpdate: (element: FormElement) => void;
  onElementSelect?: (element: FormElement) => void;
  onHeaderStyleChange: (styles: { backgroundColor: string; textColor: string }) => void;
  activeLeftTab: 'basic' | 'elements';
  headerStyles: { backgroundColor: string; textColor: string };
  formElements: FormElement[];
  showUploadDocuments: boolean;
  setShowUploadDocuments: (value: boolean) => void;
  formRules: DSRFormBuilderControlRules[];
  setFormRules: (value: DSRFormBuilderControlRules) => void;
  hasUnsavedChanges: boolean;
  transformElementToControl: (element: FormElement, isUpdate?: boolean) => CreateFormPayload;
}

export interface FormElementOption {
  id: string;
  label: string;
  value: string;
  selected?: boolean;
}

export interface FormElement {
  id: string;
  type:
    | 'input'
    | 'textarea'
    | 'select'
    | 'radio'
    | 'checkbox'
    | 'custom_select'
    | 'date'
    | 'file'
    | 'number';
  label: string;
  placeholder?: string | null;
  required: boolean;
  errorMessage?: string;
  options?: FormElementOption[];
  rows?: number;
  question?: string;
  helpText?: string;
  is_optional: boolean;
  section?: string;
  question_id?: number | null;
  order: number;
  // is_custom: boolean;
  is_disable: boolean;
  rules?: {
    // Make it explicit this is optional
    options: string;
    showOrHide: 'show' | 'hide';
    questionList: string[];
    questionID: string;
  }[];
  rule_applied?: boolean;
  endpoint: string | null;
}

export interface FormElementProps {
  element: FormElement;
  onDelete: (id: string) => void;
  onSelect: (element: FormElement) => void;
  isSelected: boolean;
}

export interface CreateFormControlField {
  id: number;
  name: string;
  selected: boolean;
}

export interface CreateFieldControl {
  is_optional: boolean;
  id: number;
  title: string;
  description: string | null;
  artifact_type: string;
  is_attachment: boolean;
  question: string | null;
  fields: CreateFormControlField[];
  extra_input: boolean;
  extra_input_type: string;
  extra_input_fields: any;
  endpoint: string | null;
  is_custom: boolean;
  question_id?: number | null;
  order: number;
  is_disabled: boolean;
  rules?: {
    options: string;
    showOrHide: 'show' | 'hide';
    questionList: string[];
    questionID: string;
  }[];
  rule_applied?: string;
}

export interface FormCondition {
  id: string;
  options?: string;
  showOrHide?: string;
  questionList?: string[];
  field?: string;
  operator?: string;
  value?: string;
}

export interface CreateFormPayload {
  form_id?: string;
  customer_id?: number;
  title: string;
  description: string;
  artifact_type: string;
  is_attachment: boolean;
  question: string;
  fields: CreateFormControlField[];
  extra_input?: boolean;
  extra_input_type?: string;
  is_optional?: boolean;
}

export interface DsrPublicFormElement {
  id: string;
  type: string;
  label: string;
  placeholder: string;
  description: string;
  required: boolean;
  formRules: DSRFormBuilderControlRules[];
  question: string;
  helpText: string;
  options?: { id: string; label: string; value: string }[];
  is_optional: boolean;
  endpoint: string | null;
  is_custom: boolean | null;
  rules?: {
    // Make it explicit this is optional
    options: string;
    showOrHide: 'show' | 'hide';
    questionList: string[];
    questionID: string;
  }[];
  rule_applied: boolean;
}

export interface DsrPublicFormValues {
  [key: string]: string | string[] | File | Date | { url: string; original_name: string } | null;
}

export interface DsrPublicContentData {
  logoUrl: string;
  headerBackgroundColor: string;
  textSize: string;
  fontFamily: string;
  fontColor: string;
  paragraphContent: string;
  headerTextColor: string;
  name: string;
}

export interface DSREmailVerificationInterface {
  pathName: string;
}

export interface FormPreviewProps {
  elements: FormElement[];
}

export interface DSRThankYouPageInterface {
  pathName: string;
}
export interface AddCategoryModalProperties {
  handleSubmit: (value: any) => void;
  isOpen: boolean;
  setAddTaskModalOpen: (value: boolean) => void;
  formId: string;
}

export interface AddCategoryFormData {
  name: string;
}

export interface ControlFormControl {
  id: number;
  title: string;
  content?: string;
  description: string | null;
  artifact_type: string;
  is_attachment: boolean;
  question: string | null;
  fields: any[];
  extra_input: boolean;
  extra_input_type: string;
  extra_input_fields: any;
  endpoint: string | null;
}

export interface ControlFormProps {
  controlData?: ControlFormControl[];
}

export interface FormBuilderCategoryInterface {
  id: string;
  label: string;
  controlData: any;
}

export interface FormBuilderCategoryControlField {
  id: number;
  name: string;
  selected: boolean;
}

export interface FormBuilderCategoryControl {
  id: number;
  title: string;
  description: string | null;
  artifact_type: string;
  is_attachment: boolean;
  question: string | null;
  fields: FormBuilderCategoryControlField[];
  extra_input: boolean;
  extra_input_type: string;
  extra_input_fields: any;
  endpoint: string | null;
}

export interface FormBuilderCategoryApiResponse {
  success: boolean;
  status_code: number;
  message: string;
  result: {
    count: number;
    rows: Array<{
      id: number;
      name: string;
    }>;
  };
  time: number;
}
export interface FormBuilderTable {
  id: number | string;
  formName: string;
  published: boolean | string;
  updatedAt: string | Date;
  actionVersion: string;
  url: string;
  busi_unit_id: string;
  Group: {
    id: number;
    name: string;
  } | null;
  regulation_id: number[];
  translations?: any[];
  allCategoryFormBuilder?: any[];
  authentication_type?: string;
  enable_verification?: boolean;
  content?: any;
}
export interface FormBuilderType {
  name: string;
  busi_unit_id: string;
  regulation_id?: number[];
  enable_verification: boolean;
  authentication_type?: string;
}

export type FormControlType = {
  id: string;
  title: string;
  description?: string;
  content?: string;
  artifact_type: string;
  fields?: { id: number; name: string; selected: boolean }[];
};

export type FormControlProps = {
  control: FormControlType | null;
  onEdit: (id: string) => void;
  isEditing: boolean;
  onUpdate: () => void;
  onCancel: () => void;
  newTitle: string;
  newContent: string;
  onTitleChange: (value: string) => void;
  onContentChange: (value: string) => void;
};

export interface StepperFormActionsProperties {
  handleClick?: () => void;
}
export interface FormBuilderMailTemplateProps {
  logoUrl: string;
  setLogoUrl: React.Dispatch<React.SetStateAction<string>>;
  headerBackgroundColor: string;
  textSize: string;
  fontFamily: string;
  fontColor: string;
  headerTextColor: string;
  onReset: () => void;
  setselectedBackgroundHeaderColor?: React.Dispatch<React.SetStateAction<string>>;
  setSelectedFontColor?: React.Dispatch<React.SetStateAction<string>>;
  setSelectedHeaderTextColor?: React.Dispatch<React.SetStateAction<string>>;
  setSelectedFontFamily?: React.Dispatch<React.SetStateAction<string>>;
  setSelectedTextSize?: React.Dispatch<React.SetStateAction<string>>;
  isFormRepository?: boolean;
  setIsDialogOpen?: React.Dispatch<React.SetStateAction<boolean>>;
}

export interface FormBuilderMailTempstepslabel {
  label: string;
}

export interface StepInternalConfig {
  index: number;
  isCompletedStep?: boolean;
  isCurrentStep?: boolean;
  isLastStep?: boolean;
}

export interface StepperEditCategoryInterface {
  id: string;
  label: string;
  controlData: any;
}

export interface StepperEditCategoryProperties {
  isEditMode?: boolean;
  stepperData: StepperEditCategoryInterface[];
}

export interface StepProps extends React.HTMLAttributes<HTMLLIElement> {
  label?: string | React.ReactNode;
  description?: string;
  icon?: IconType;
  state?: 'loading' | 'error';
  checkIcon?: IconType;
  errorIcon?: IconType;
  isCompletedStep?: boolean;
  isKeepError?: boolean;
  onClickStep?: (step: number, setStep: (step: number) => void) => void;
  workFlow?: string;
}

export interface StepSharedProps extends StepProps {
  isLastStep?: boolean;
  isCurrentStep?: boolean;
  index?: number;
  hasVisited: boolean | undefined;
  isError?: boolean;
  isLoading?: boolean;
}
export type VerticalStepProperties = StepSharedProps & {
  children?: React.ReactNode;
  workFlow?: string;
  isAutomationEnabled?: boolean;
};

export interface TaskUser {
  firstName: string;
  lastName: string;
}

export interface NewTaskDataItem {
  documents: string[];
  assigned_to: string;
  TaskDocuments: string[];
  id: number;
  title: string;
  assignee_id: string[];
  created_date: Date | string;
  end_date: Date | string;
  department: string;
  priority: string;
  attachment: number;
  activepieces_automation_id: string | null;
  guidance_text: string;
  users?: TaskUser;
  due_days: number;
}
export interface TaskFormData {
  id: number;
  stage_id: number;
  title: string;
  guidance_text: string;
  activepieces_automation_id?: string | null;
  workflow_id?: string;
  assignee_id: string[];
  due_date?: Date | string;
  start_date?: Date | string;
  users?: TaskUser;
  due_days: number;
}
export interface AddDsrWorkflowTasksProperties {
  dsrAddWorkflowTableData: NewTaskDataItem[];
  activeStep: number;
  isEditMode: boolean;
}
export interface AutomationItem {
  value: string;
  label: string;
  id: string;
  steps: TaskStep[];
}

export interface TaskStep {
  label: string;
  description: string;
}
export interface AutomationStates {
  [key: number]: boolean;
}

export interface AutomationInterface {
  activepieces_automation_id: string | null;
}

export interface EditWorkFlowFormData {
  workFlowName: string;
  module_name: string;
}

export interface FormRepoPreviewProps {
  currentStep: any;
  currentStepIndex: number;
  steps: any[];
  stepsArray: any[];
}

export interface FormRepoControlItem {
  id: string;
  title: string;
  type: 'input' | 'radio' | 'readonly';
  content?: string;
  description?: string;
  required?: boolean;
  options?: { name: string }[];
}

export interface FormRepoStepData {
  controlData: FormRepoControlItem[];
}

export interface LogoUploadProps {
  onLogoChange: (logoUrl: string) => void;
}
export interface UpdateFormRepositoryProps {
  logoUrl: string;
  setLogoUrl: React.Dispatch<React.SetStateAction<string>>;
  headerBackgroundColor: string;
  textSize: string;
  fontFamily: string;
  fontColor: string;
  headerTextColor: string;
  onReset: () => void;
  setselectedBackgroundHeaderColor?: React.Dispatch<React.SetStateAction<string>>;
  setSelectedFontColor?: React.Dispatch<React.SetStateAction<string>>;
  setSelectedHeaderTextColor?: React.Dispatch<React.SetStateAction<string>>;
  setSelectedFontFamily?: React.Dispatch<React.SetStateAction<string>>;
  setSelectedTextSize?: React.Dispatch<React.SetStateAction<string>>;
  isFormRepository?: boolean;
  setIsDialogOpen?: React.Dispatch<React.SetStateAction<boolean>>;
}

export interface NavbarItems {
  crmType: string;
  navHeadings: string[];
  pathName: string;
}
export interface DashBoardChartData {
  label: string[];
  count: number[];
  backgroundColor: string[];
}
export interface RequestTable {
  ticketData: any;
}

export interface DurationItem {
  name: string;
  days: number;
  description: string;
}
export type DsrAssigneeRowData = {
  id: string | number;
  dsr_id: string;
  subject_type: string;
  full_name: string;
  request_type: string;
  owner: string;
  deadline: string;
  DataSubject: DsrAssigneeSubjectData;
  DsrRequestType: { flowtype: string };
  deadline_date: string;
  start_date: string;
  createdAt: string;
  User: DsrAssigneeUserData;
  status: string;
  extended: string;
  business_unit: any;
};
export interface DsrAssigneeSubjectData {
  id: number;
  first_name: string;
  last_name: string;
  relationship: string;
}
export interface DsrAssigneeUserData {
  id: number;
  firstName: string;
  lastName: string;
  relationship: string;
}

export interface DsrViewAssigneData {
  searchTerm: string;
}
export interface MyRequestDetailData {
  dsr_id: any;
  DsrRequestType?: any;
  created_date?: any;
  assigned_date?: any;
  DataSubject?: any;
  deadline_date?: any;
  customer_id: number;
}
export type DataRequestItemPropperties = {
  id: string;
  type: string;
  business_unit: string;
  request_date: string;
  dsr_id: number;
  status: string;
};

export interface AllRequestFormData {
  id: number;
  dsr_id: string;
  data_subject_id: number;
  customer_id: number;
  user_id: number;
  request_type: number;
  description: string;
  request_date: string;
  completion_date: string | null;
  is_internal_request: boolean;
  dsr_return_preference: string;
  status: string;
  assignee_id: number;
  assigned_date: string;
  deadline_date: string;
  extended: string;
  workflow_step_id: number;
  reject_reason: string | null;
  data_discovery: string;
  business_unit: string;
  is_acknowledge_mail_sent: string;
  createdAt: string;
  updatedAt: string;
  deletedAt: string | null;
  DsrRequestType: {
    id: number;
    flowtype: string;
    dsr_id: string;
    customer_id: number;
    created_by: number;
    workflow_status: string;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
  };
  DataSubject: {
    id: number;
    is_data_subject: boolean;
    first_name: string;
    last_name: string;
    unique_identification_type: string;
    unique_identification_number: string;
    address_1: string;
    address_2: string;
    country_id: number;
    state_id: number;
    city: string;
    postal_code: number;
    relationship: string;
    email: string;
    phone_no: string;
    dob: string;
    third_party_name: string | null;
    third_party_practice_name: string | null;
    third_party_email: string | null;
    third_party_contact_number: string | null;
    second_address_1: string | null;
    second_address_2: string | null;
    second_country_id: number | null;
    second_state_id: number | null;
    second_city: string | null;
    second_postal_code: number | null;
    customer_id: number;
    user_id: number;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
    country: {
      id: number;
      sort_name: string;
      country_name: string;
      country_code: string;
      createdAt: string;
      updatedAt: string;
      deletedAt: string | null;
    };
  };
  RequestDocuments: any[]; // Assuming this can be any array
  User: {
    id: number;
    tsp_user_id: string | null;
    firstName: string;
    lastName: string;
    role_id: number;
    email: string;
    profile_image: string | null;
    mpin: string | null;
    language: string | null;
    country_code: string;
    phone: string;
    is_phone_number_verified: boolean;
    tnc_accepted: boolean;
    is_notifies: boolean;
    otp: number;
    marketing_email_accepted: boolean;
    access_token: string;
    status: string;
    address: string | null;
    createdAt: string;
    updatedAt: string;
    deletedAt: string | null;
  };
  step_status: string;
  joint_party: boolean;
  role: string;
}
//* Tasks Interfaces

export interface DsrReduxTaskDataItem {
  id: number;
  title?: string;
  assigned_to?: [];
  created_date?: Date | string;
  end_date?: Date | string;
  department?: string;
  progress?: string;
  steps?: string;
  priority?: string;
  attachment?: number;
  start_date?: string | undefined;
  due_date?: string | undefined;
  guidance_text?: string;
  TaskDocuments?: DsrReduxUploadedFile[];
  users?: [];
  department_id: string;
  assignee_id: string[];
  Department?: {
    name: string;
  };
  RequestTypeStage?: {
    step_title: string;
  };
  documents: [];
  due_days: number;
  createdAt?: Date | string;
}
interface DsrReduxUploadedFile {
  id: string;
  file: File;
  original_name: string;
  size: number;
  type: string;
}
export interface DsrReduxDepartment {
  id: number;
  name: string;
  parent_id: any;
  customer_id: number;
  spoc_id: number;
}
export interface NewTaskDataTableProperties {
  data: DsrReduxTaskDataItem[];
  columns: ColumnDef<DsrReduxTaskDataItem>[];
  loading: boolean;
  onRowClick?: (task: any) => void;
}
export interface TaskDataTableProperties {
  data: DsrReduxTaskDataItem[];
  columns: ColumnDef<DsrReduxTaskDataItem>[];
  loading: boolean;
  handleUpdate: (data: DsrReduxFormData) => void;
  showTaskView: boolean;
  setShowTaskView: (value: boolean) => void;
  userData: DsrReduxUserDetails[];
  isTaskOpen?: boolean;
  departments: DsrReduxDepartment[];
  groupId: string;
  requestStatus?: string;
}
export interface DsrReduxUserDetails {
  id: number;
  firstName: string;
  lastName: string;
}

export interface DsrReduxFormData {
  id: number;
  stage_id: number | undefined;
  title: string;
  department_id: string;
  start_date: string | undefined;
  due_date: string | undefined;
  guidance_text: string;
  assignee_id: string[];
  TaskDocuments: DsrReduxUploadedFile[];
}
export interface DsrReduxChatViewProperties {
  showChatView: boolean;
  setShowChatView: (value: boolean) => void;
}
//* Attachment Interfaces
export interface DsrReduxAttachmentDataItem {
  id: number;
  name?: string;
  added_by?: string;
  created_date?: Date | string;
  end_date?: Date | string;
  department_id: string;
  assignee_id: string[];
  url?: string;
  documents: [];
}

export interface DsrReduxAttachmentDataTableProperties {
  data: DsrReduxAttachmentDataItem[];
  columns: ColumnDef<DsrReduxAttachmentDataItem>[];
  loading: boolean;
}

export interface DSRFormBuilderControlRules {
  options: string;
  showOrHide: string;
  questionList: string[];
  questionID: string;
}

export interface FormattedRule {
  [key: string]: {
    options: string;
    showOrHide: string;
    questionList: string[];
  };
}
