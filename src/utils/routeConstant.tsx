//public routes
export const HOMEPAGE = '/';
export const LOGIN = '/login';
export const LOGIN_WITH_PARAM = '/login/:id';
export const SIGNUP = '/signup';
export const CANNOT_LOGIN = '/cannot-login';
export const FRAMER_Link = 'https://www.gotrust.tech/Privacy-Policy';
export const RECOVERY = '/recovery';
export const ORGANISATION_DETAILS = '/organisation-details';
export const VERIFY_EMAIL = '/verify-email';
export const SET_PASSWORD = '/set-password';
export const FORGOT_PASSWORD = '/forgot-password';
export const UNAUTHORIZED = '/unauthorized';
export const INTERNAL_SERVER_ERROR = '/internal-server';
export const PRIVACY_CENTER = '/privacy-centre';
export const PRIVACY_CENTER_NOTICE = `${PRIVACY_CENTER}/privacy-notice`;
export const NOTICE = '/notice/:id';
export const PRIVACY_CENTER_COOKIE = `${PRIVACY_CENTER}/cookies-preference`;
export const PRIVACY_CENTER_CONSENT_PURPOSES = `${PRIVACY_CENTER}/consent-purposes-preference`;
export const PRIVACY_CENTER_DSR = `${PRIVACY_CENTER}/dsr`;
export const GOTRUST_PREFERENCE_FORM = '/pf';
export const GOTRUST_COOKIE_POLICY_NOTICE = '/cp';
export const GOTRUST_CONSENT_FORM = '/consent-form';
export const GOTRUST_CONSENT_FORM_VERIFICATION = '/consent-form/verification';
export const PUBLIC_ROUTES = [HOMEPAGE, LOGIN, LOGIN_WITH_PARAM, UNAUTHORIZED];

// ------------------------------protected route----------------------------------
export const HOME = '/home';
// export const SCANLINK = '/scanlink';
// export const ONBOARDING = '/onboarding';
export const ONBOARDING_QUESTIONS = '/onboarding-questionnaire';

//configuration routes
export const CUSTOMIZE = '/customization';
export const VISUALIZATION = '/visualization';

// Finance
export const FINANCE = '/finance/*';
export const INVOICE = '/finance/invoice';

// Role Management
export const ROLE_MANAGEMENT = '/crm/role-management';
export const ROLE_MANAGEMENT_ADD_ROLE = '/crm/role-management/addrole';
export const ROLE_MANAGEMENT_EDIT_ROLE = '/crm/role-management/editrole';
export const ROLE_MANAGEMENT_ROLE_DETAILS = '/crm/role-management/roledetails';
export const ROLE_MANAGEMENT_EDIT_ROLE_DETAILS = '/crm/role-management/editroledetails';

// Customer Management
export const CUSOMER_MANAGEMENT = '/crm/customer-management';
export const CUSTOMER_MANAGEMENT_ADD_CUSTOMER = '/crm/customer-management/add';
export const CUSTOMER_MANAGEMENT_EDIT_CUSTOMER = '/crm/customer-management/edit';
export const CUSTOMER_MANAGEMENT_VIEW_CUSTOMER = '/crm/customer-management/view';

// User Management
export const USER_MANAGEMENT = '/crm/user-management';

// Company Structure
export const COMPANY_STRUCTURE = '/crm/company-structure';
// export const EDIT_GROUP = '/crm/company-structure/edit-group-user';
export const SELECTED_VIEW_GROUP_DETAILS = '/crm/company-structure/group-details';

// Profile
export const PROFILE = '/setting/profile';
export const CHANGE_PASSWORD = '/setting/profile/change-password';

// Common
export const BLOGS = '/about-gotrust/blogs';
export const ABOUT_GOTRUST = '/about-gotrust';
export const COMMUNITY = '/about-gotrust/community';
export const VIDEOS = '/about-gotrust/how-to-use/videos';
export const DOCUMENTS = '/about-gotrust/how-to-use/documents';
export const VIEW_DETAIL_BLOGS = '/about-gotrust/blogs/detailed-blog';

// ROPA
export const ACTIVITY_LOG = '/data-mapping/ropa/';
export const ROPA_VIEW = '/data-mapping/ropa/view';
export const ROPA_RIVIEW = '/data-mapping/ropa/review';
export const ROPA_REGISTER = '/data-mapping/ropa/register/:id';
export const ROPA_DASHBOARD = '/data-mapping/ropa-dashboard';
export const ROPA_ADD_QUESTION = '/data-mapping/ropa/question';
export const ACTIVITY_AUDIT_LOG = '/data-mapping/ropa/audit-log';
export const ROPA_VIEW_AUDIT_LOG = '/data-mapping/ropa/view/audit-log';
export const UNSTRUCTURED_DATA_MAPPING = '/data-mapping/ropa/pii-list';
export const PII_LIST = '/data-mapping/ropa/unstructured-data-inventory';
export const ROPA_BASIC_INFORMATION = '/data-mapping/ropa/basics-information';
export const PII_HANDBOOK = '/data-mapping/data-insights/pii-handbook';

// Data Mapping
export const FILE_CLASSIFICATION = '/data-mapping/file-classification';
export const PII_ANALYSIS = '/data-mapping/pii-analysis';

export const STRUCTURED_DATA_CATALOGUE = '/data-catalogue/structure';
export const UNSTRUCTURED_DATA_CATALOGUE = '/data-catalogue/unstructure';
export const UNSTRUCTURED_SERVICES = '/data-catalogue/unstructure/services';
export const STRUCTURED_SERVICES = '/data-catalogue/structure/services';
export const UNSTRUCTURED_INGESTION = '/data-catalogue/unstructure/ingestions';
export const STRUCTURED_INGESTION = '/data-catalogue/structure/ingestions';

export const DATA_CATALOGUE_DASHBOARD = '/data-catalogue/dashboard';
export const PII_LIST_TABLE = '/data-catalogue/dashboard/pii-list';
export const FLOW_DIAGRAM = '/data-mapping/flow-diagram';
export const DATA_INSIGHTS = '/data-mapping/data-insights';

export const DATA_CATALOGUE_V0 = '/ropa/data-catalogue/structure/v0';
export const DATA_CATALOGUE_UNSTRUCTURED_V0 = '/ropa/data-catalogue/unstructure/v0';

// Support
export const SUPPORT = '/support';
// export const ALL_TICKET = '/support/all-ticket';
// export const OPEN_TICKET = '/support/open-ticket';
// export const SUPPORT_DASBOARD = '/support/dasboard';
// export const CLOSE_TICKET = '/support/close-ticket';
// export const EMPLOYEE_SUPPORT = '/support/employee';
export const CREATE_TICKET = '/support/create-ticket';
export const EDIT_TICKET_DETAILS = '/support/edit-ticket';
export const VIEW_TICKET_DETAILS = '/support/view-ticket';

//Privacy and Data Protection - Risk Assessment
// export const RISK_ASSESSMENT = '/assessment/privacy/*';
export const ASSESSMENT_DASHBOARD = '/assessment-management';
export const ASSESSMENT_VIEW = '/assessment-management/task-overview/view';
export const ASSESSMENT_REVIEW = '/assessment-management/task-overview/review';
export const ASSESSMENT_ACTION = '/assessment-management/task-overview/controls/:id';
export const ASSESSMENT_VIEW_AUDIT_LOG = '/assessment-management/task-overview/view/audit-log';
export const ASSESSMENT_ADD_QUESTION = '/assessment-management/task-overview/controls/add-question';
export const ASSESSMENT_MANAGEMENT_TASK_OVERVIEW = '/assessment-management/task-overview';
export const ASSESSMENT_MANAGEMENT_TASK_OVERVIEW_AUDIT_LOG =
  '/assessment-management/task-overview/audit-log';
export const ASSESSMENT__TEMPLATES = '/assessment-management/templates';
export const ASSESSMENT__TEMPLATES_VIEW = '/assessment-management/templates/view';
//Policy Management
// export const POLICY = '/dashboard/policy';
// export const ALL_POLICY = '/policy/all-policy';
// export const PRIVACY_POLICY = '/privacy/*';
export const PRIVACY_POLICY_MANAGEMENT = '/privacy/privacy-policy/*';
export const PRIVACY_POLICY_CREATE_POLICY = '/privacy/privacy-policy/policy/view/*';
export const PRIVACY_POLICY_CREATE_POLICY_TEMPLATE =
  '/privacy/privacy-policy/policy/create-template';
export const POLICY_MANAGEMENT_DASHBOARD = '/privacy/privacy-policy/';
export const POLICY_MANAGEMENT_COOKIE_POLICY = '/privacy/privacy-policy/cookie-policy';
export const POLICY_MANAGEMENT_RETENTIION_POLICY = '/privacy/privacy-policy/retention-policy';
export const POLICY_MANAGEMENT_COOKIE_POLICY_CREATE =
  '/privacy/privacy-policy/cookie-policy/create';
export const POLICY_MANAGEMENT_COOKIE_POLICY_DETAIL = '/privacy/privacy-policy/cookie-policy/edit';
export const POLICY_MANAGEMENT_TASK_OVERVIEW = '/privacy/privacy-policy/policy';
export const POLICY_MANAGEMENT_AUDIT_LOG = '/privacy/privacy-policy/audit-log';
export const POLICY_PRIVACY_NOTICE = '/privacy/privacy-policy/privacy-notice';
export const POLICY_CREATE_PRIVACY_NOTICE = '/privacy/privacy-policy/privacy-notice/create';
export const POLICY_VIEW_PRIVACY_NOTICE = '/privacy/privacy-policy/privacy-notice/view';
// export const ASSESSMENT_CONTROLS = `/assessment/privacy/controls`;
// export const ASSESSMENT_AUDIT_LOG = `/assessment/privacy/audit-log`;

//Vendor Risk Management
export const VENDOR_RISK_MANAGEMENT_DASHBOARD = '/vrm/dashboard';
// export const VENDOR_RISK_MANAGEMENT_REVIEW = '/vrm/dashboard/review';
export const VRM_TASK_OVERVIEW = '/vrm/task-overview';
export const VRM_TASK_OVERVIEW_AUDIT_LOG = '/vrm/task-overview/vendor-details/:id/audit-log';
export const VRM_TASK_OVERVIEW_CREATE = '/vrm/task-overview/create';
export const VRM_VENDOR_DETAILS = '/vrm/task-overview/vendor-details/:id';
export const VRM_VIEW_INTERNAL_ASSESSMENT = '/vrm/task-overview/internal-assessment/view';
export const VRM_VIEW_INTERNAL_ASSESSMENT_AUDIT_LOG =
  '/vrm/task-overview/internal-assessment/view/audit-log';
export const VRM_VIEW_VENDOR_ASSESSMENT = '/vrm/task-overview/vendor-assessment/view';
export const VRM_VIEW_VENDOR_ASSESSMENT_AUDIT_LOG =
  '/vrm/task-overview/vendor-assessment/view/audit-log';
export const VRM_ASSESSMENT_ACTION = '/vrm/task-overview/controls';
export const VRM_ASSESSMENT_REVIEW = '/vrm/task-overview/review';
export const VRM_ASSESSMENT_MITIGATION = '/vrm/task-overview/mitigation';
export const VRM_ASSESSMENT_ADD_QUESTION = '/vrm/task-overview/controls/add-question';
export const VRM_ASSESSMENT_TEMPLATES = `/vrm/lab/templates`;
export const VRM_TEMPLATES_VIEW = '/vrm/lab/templates/view';
export const VIEW_INTERNAL_ASSESSMENT_QUESTIONS = '/vrm/task-overview/assessment/view-questions';

// Cookie Consent Management
export const COOKIE_CONSENT_MANAGEMENT = '/cookie-consent-management';
export const COOKIE_CONSENT_DOMAIN = `${COOKIE_CONSENT_MANAGEMENT}/cookie-consent-domain`;
export const COOKIE_CONFIGURATION = `${COOKIE_CONSENT_DOMAIN}/config`;
export const COOKIE_POLICY = `${COOKIE_CONSENT_MANAGEMENT}/cookie-policy`;
export const COOKIE_POLICY_CREATE = `${COOKIE_POLICY}/create`;
export const VIEW_COOKIE_POLICY = `${COOKIE_POLICY}/view`;
export const COOKIE_POLICY_DETAILS = `${COOKIE_POLICY}/edit`;
export const RECORDED_CONSENTS = `${COOKIE_CONSENT_MANAGEMENT}/recorded-consents`;
export const COOKIE_DICTIONARY = `${COOKIE_CONSENT_MANAGEMENT}/cookie-dictionary`;
// export const COOKIE_CONSENT_USER_GUIDE = `${COOKIE_CONSENT_MANAGEMENT}/user-guide`;

// Universal Consent Management
export const UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD = '/consent-management';
export const UNIVERSAL_CONSENT_MANAGEMENT_FORMCENTRE = '/consent-management/form-center';
export const UNIVERSAL_CONSENT_MANAGEMENT_CONSENTUPLOADS = '/consent-management/consent-uploads';
export const UNIVERSAL_CONSENT_MANAGEMENT_PREFERENCECENTRE =
  '/consent-management/ucm-lab/preference-center';
export const UNIVERSAL_CONSENT_ADD_PREFERENCECENTRE_CENTER =
  '/consent-management/ucm-lab/preference-center-add';
export const UNIVERSAL_CONSENT_UPDATE_PREFERENCECENTRE_CENTER =
  '/consent-management/preference-center-update';
export const UNIVERSAL_CONSENT_CUSTOME_PARAMETERS = '/consent-management/custom-parameters';
export const UNIVERSAL_CONSENT_CONSENT_PURPOSE = '/consent-management/consent-purpose';
export const UCM_PRIVACY_NOTICE = `${UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}/privacy-notice`;
export const UCM_PRIVACY_NOTICE_DETAILS = `${UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}/privacy-notice/:id`;
export const VIEW_UCM_PRIVACY_NOTICE_DETAILS = `${UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}/privacy-notice/view/:id`;
export const UCM_COLLECTION_TEMPLATE = `${UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}/collection-templates`;
export const UCM_ADD_COLLECTION_TEMPLATE = `${UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}/ucm-lab/add-collection-templates`;
export const UCM_ADD_COLLECTION_TEMPLATE_POC = `${UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}/add-lead-onboarding`;
export const UCM_SUBJECT_CONSENT_TYPES = `${UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}/subject-consent-types`;
export const UCM_SUBJECT_CONSENT_TYPES_DETAILS = `${UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}/subject-consent-types/details`;
export const UCM_SUBJECT_CONSENT_LIST = `${UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}/subject-consent-list`;
export const UCM_SUBJECT_CONSENT_MANAGER = `${UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}/subject-consent-manager`;
export const UCM_SUBJECT_CONSENT_MANAGER_TEMPLATE = `${UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}/subject-consent-manager/template`;
export const UCM_SUBJECT_CONSENT_MANAGER_TEMPLATE_DETAILS = `${UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}/subject-consent-manager/template/:id`;
export const UCM_SUBJECT_CONSENT_MANAGER_LIST = `${UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}/subject-consent-manager/list`;
export const UCM_SUBJECT_CONSENT_MANAGER_LIST_DETAILS = `${UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}/subject-consent-manager/list/:id`;
export const UCM_SUBJECT_CONSENT_LIST_DETAILS = `${UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}/subject-consent-list/:id`;
// export const UCM_PROCESSING_PURPOSE = `${UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}/processing-purpose`;
export const UCM_TEMPLATES = `${UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}/ucm-lab/templates`;
export const UCM_LAB = `${UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}/ucm-lab`;
export const UCM_SOURCE = `${UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD}/source`;
export const UCM_FORM = `${UCM_SOURCE}/form`;
export const UCM_SOURCE_CONSENT_UPLOAD = `${UCM_SOURCE}/consent-upload`;
export const UCM_FORM_CREATE = `${UCM_FORM}/create-form`;
export const UCM_PROCESSING_CATEGORY = `${UCM_LAB}/processing-category`;
export const UCM_PROCESSING_PURPOSE = `${UCM_LAB}/processing-purpose`;
export const UCM_CONSENT_PURPOSE = `${UCM_LAB}/consent-purpose`;
export const UCM_PII = `${UCM_LAB}/pii`;

// Data Subject Rights Management
export const DATA_SUBJECT_RIGHTS = '/data-subject-rights';
export const DSR_TASK_OVERVIEW = `${DATA_SUBJECT_RIGHTS}/task-overview`;
export const DSR_TASK_OVERVIEW_APPROVED = `${DSR_TASK_OVERVIEW}/approved-requests`;
export const DSR_TASK_OVERVIEW_REJECT_IN_PROGRESS = `${DSR_TASK_OVERVIEW}/reject-in-progress-requests`;
export const DSR_TASK_OVERVIEW_REJECTED = `${DSR_TASK_OVERVIEW}/rejected-requests`;
export const DSR_TASK_OVERVIEW_COMPLETED = `${DSR_TASK_OVERVIEW}/completed-requests`;
export const DSR_TASK_OVERVIEW_ARCHIVED = `${DSR_TASK_OVERVIEW}/archived-requests`;
export const DSR_TASK_OVERVIEW_CREATE = `${DSR_TASK_OVERVIEW}/create-request`;
export const ASSIGNEE_REQUEST_FORM = `${import.meta.env.VITE_APP_GO_TRUST_BASE_API}/dsr/request-form/*`;
export const GUEST_REQUEST_FORM = `${import.meta.env.VITE_APP_GO_TRUST_DSR_BASE_API}/dsr/request-form/*`;
export const DSR_MY_REQUEST = `${DATA_SUBJECT_RIGHTS}/my-request`;
export const DSR_EMAIL_VER = `${DATA_SUBJECT_RIGHTS}/my-request/email-verification/*`;
export const DSR_MY_REQUEST_DETAILS = `${DATA_SUBJECT_RIGHTS}/my-request/details/*`;
export const DSR_ASSIGNEE_VIEW = `${DATA_SUBJECT_RIGHTS}/dsr-assignee-view`;
export const DSR_ASSIGNEE_VIEW_DETAILS = `${DSR_TASK_OVERVIEW}/dsr-assignee-view-details`;
export const DSR_TASK_OVERVIEW_VIEW_PENDING_REQ = `${DSR_TASK_OVERVIEW}/pending/view`;
export const DSR_TASK_OVERVIEW_VIEW_APPROVED_REQ = `${DSR_TASK_OVERVIEW}/approved/view`;
export const DSR_TASK_OVERVIEW_VIEW_REJECTED_REQ = `${DSR_TASK_OVERVIEW}/rejected/view`;
export const DSR_TASK_OVERVIEW_VIEW_COMPLETED_REQ = `${DSR_TASK_OVERVIEW}/completed/view`;
export const DSR_TASK_OVERVIEW_VIEW_ARCHIVED_REQ = `${DSR_TASK_OVERVIEW}/archived/view`;
export const DSR_TASK_OVERVIEW_VIEW_REJECTED_IN_PROGRESS = `${DSR_TASK_OVERVIEW}/rejected-in-progress/view`;
export const DSR_EMAIL_TEMPLATES = `${DATA_SUBJECT_RIGHTS}/lab/email-template`;
export const DSR_FORM_REPOSIOTRY = `${DATA_SUBJECT_RIGHTS}/lab/form-repository`;
export const DSR_RETENTION_SCHEDULE = `${DATA_SUBJECT_RIGHTS}/lab/retention-schedule`;
export const DSR_TASK_OVERVIEW_VIEW_ASSIGNEE = `${DSR_TASK_OVERVIEW}/view`;
export const DSR_MY_TASK = `${DATA_SUBJECT_RIGHTS}/tasks`;
export const DSR_MY_TASK_VIEW = `${DATA_SUBJECT_RIGHTS}/tasks/view`;
export const DSR_FORM_BUILDER = `${DATA_SUBJECT_RIGHTS}/lab/form-builder`;
export const DSR_FORM_BUILDER_CREATE_FORM = `${DATA_SUBJECT_RIGHTS}/lab/form-builder/create-form`;
export const DSR_FORM_BUILDER_VIEW = `${DATA_SUBJECT_RIGHTS}/lab/form-builder/view`;
export const DSR_ADD_QUESTION = `${DATA_SUBJECT_RIGHTS}/lab/form-builder/create-form/question`;
export const DSR_FORM_BUILDER_REVIEW = `${DATA_SUBJECT_RIGHTS}/lab/form-builder/create-form/form-builder-review`;
export const DSR_FORM_TRANSLATION = `${DATA_SUBJECT_RIGHTS}/lab/form-builder/translate-form`;

// GLOBAL--Workflow
export const GLOBAL = '/workflow-global';

export const GLOBAL_WORKFLOW_TABLE = `${GLOBAL}`;
export const GLOBAL_ADD_WORKFLOW = `${GLOBAL}/workflow-global/add`;
export const GLOBAL_EDIT_WORKFLOW = `${GLOBAL}/workflow-global/edit`;
export const GLOBAL_VIEW_WORKFLOW = `${GLOBAL}/workflow-global/view`;

// DSR--Workflow
export const DSR_WORKFLOW_TABLE = `${DATA_SUBJECT_RIGHTS}/lab/workflow`;
export const DSR_ADD_WORKFLOW = `${DATA_SUBJECT_RIGHTS}/lab/workflow/add`;
export const DSR_EDIT_WORKFLOW = `${DATA_SUBJECT_RIGHTS}/lab/workflow/edit`;
export const DSR_VIEW_WORKFLOW = `${DATA_SUBJECT_RIGHTS}/lab/workflow/view`;
export const DSR_REQUEST_FORM = `${DATA_SUBJECT_RIGHTS}/lab/email-template/create-template`;
// UCF
export const UCF = '/universal-control-system';
export const UCF_IMPROVENENT = `${UCF}/improvements`;
export const UCF_ACTIONS = `${UCF}/actions`;
export const UCF_ONBOARDING = `${UCF}/onboarding`;

//Privacy Ops
export const PRIVACY_OPS = `/privacy-ops`;
export const PRIVACY_OPS_DOCUMENT_REPO = `${PRIVACY_OPS}/repository/document-repo`;
export const PRIVACY_OPS_REGULATIONS = `${PRIVACY_OPS}/regulations`;
export const PRIVACY_OPS_REGULATIONS_DETAILS = `${PRIVACY_OPS}/regulations/details`;
export const PRIVACY_OPS_RISK_REGISTER = `${PRIVACY_OPS}/risk-register`;
export const PRIVACY_OPS_RISK_REGISTER_DETAILS = `${PRIVACY_OPS}/risk-register/details`;
export const PRIVACY_OPS_RISK_REGISTER_AUDIT = `${PRIVACY_OPS}/risk-register/audit-log`;
export const PRIVACY_OPS_ASSESSMENT_REPO = `${PRIVACY_OPS}/repository/assessment-repo`;
export const PRIVACY_OPS_VRM_REPO = `${PRIVACY_OPS}/repository/vrm-repo`;
export const PRIVACY_OPS_PROCESSING_ACTIVITIES = `${PRIVACY_OPS}/repository/processing-activities`;
export const PRIVACY_OPS_PROCESSING_ACTIVITIES_DETAILS = `${PRIVACY_OPS}/repository/processing-activities/:id`;
export const PRIVACY_OPS_ASSESSMENT_REPO_DETAILS = `${PRIVACY_OPS}/repository/assessment-repo/:id`;
export const PRIVACY_OPS_ACTIVITIES_DUTIES = `${PRIVACY_OPS}/activities/duties`;
export const PRIVACY_OPS_ACTIVITIES_ACTIONS = `${PRIVACY_OPS}/activities/actions`;
export const PRIVACY_OPS_ACTIVITIES_IMPROVEMENTS = `${PRIVACY_OPS}/activities/improvements`;
export const PRIVACY_OPS_COMPLIANCE_DASHBOARD = `${PRIVACY_OPS}/risk-compliance`;
export const PRIVACY_OPS_RISK_DASHBOARD = `${PRIVACY_OPS}/risk-dashboard`;
export const PRIVACY_OPS_CONTROL_HANDBOOK = `${PRIVACY_OPS}/control-handbook`;
export const ADD_CONTROL_CATEGORY = `${PRIVACY_OPS}/control-handbook/add-control-category`;
export const PRIVACY_OPS_CONTROL_DETAILS = `${PRIVACY_OPS}/control-handbook/control-details`;

//Incident and data breach management
export const BREACH_MANAGEMENT = '/breach-management';
export const BREACH_MANAGEMENT_DASHBOARD = `${BREACH_MANAGEMENT}/dashboard`;
export const BREACH_LIST = `${BREACH_MANAGEMENT}/list`;
export const BREACH_DETAILS = `${BREACH_MANAGEMENT}/list/details`;

//Data Retention

export const DATA_RETENTION = `/data-retention`;
export const RETENTION_RULE_LIST = `${DATA_RETENTION}/retention-rules`;
export const RETENTION_RULE_DETAILS = `${DATA_RETENTION}/rule-details`;
export const RETENTION_RULE_DETAILS_VIEW = `${DATA_RETENTION}/rule-details/view`;
export const DATA_RETENTION_DASHBOARD = `${DATA_RETENTION}`;

export const PROTECTED_ROUTE = [
  HOME,
  UNAUTHORIZED,
  SIGNUP,
  CANNOT_LOGIN,
  RECOVERY,
  ORGANISATION_DETAILS,
  VERIFY_EMAIL,
  SET_PASSWORD,
  FORGOT_PASSWORD,
  DOCUMENTS,
  VENDOR_RISK_MANAGEMENT_DASHBOARD,
  VRM_TASK_OVERVIEW_CREATE,
  VRM_TASK_OVERVIEW_AUDIT_LOG,
  VRM_TASK_OVERVIEW,
  VRM_ASSESSMENT_MITIGATION,
  VRM_ASSESSMENT_TEMPLATES,
  VIEW_INTERNAL_ASSESSMENT_QUESTIONS,
  VRM_VIEW_INTERNAL_ASSESSMENT,
  VRM_VIEW_INTERNAL_ASSESSMENT_AUDIT_LOG,
  VRM_VIEW_VENDOR_ASSESSMENT,
  VRM_VIEW_VENDOR_ASSESSMENT_AUDIT_LOG,
  VRM_ASSESSMENT_ACTION,
  VRM_ASSESSMENT_REVIEW,
  VRM_ASSESSMENT_ADD_QUESTION,
  VIDEOS,
  BLOGS,
  VIEW_DETAIL_BLOGS,
  COMMUNITY,
  FINANCE,
  INVOICE,
  ACTIVITY_LOG,
  ACTIVITY_AUDIT_LOG,
  PRIVACY_POLICY_MANAGEMENT,
  PRIVACY_POLICY_CREATE_POLICY,
  POLICY_MANAGEMENT_DASHBOARD,
  POLICY_MANAGEMENT_TASK_OVERVIEW,
  POLICY_MANAGEMENT_AUDIT_LOG,
  STRUCTURED_DATA_CATALOGUE,
  UNSTRUCTURED_DATA_CATALOGUE,
  UNSTRUCTURED_SERVICES,
  UNSTRUCTURED_INGESTION,
  DATA_CATALOGUE_DASHBOARD,
  PII_LIST_TABLE,
  DATA_CATALOGUE_V0,
  DATA_CATALOGUE_UNSTRUCTURED_V0,
  ROLE_MANAGEMENT,
  USER_MANAGEMENT,
  CUSOMER_MANAGEMENT,
  CUSTOMER_MANAGEMENT_ADD_CUSTOMER,
  CUSTOMER_MANAGEMENT_EDIT_CUSTOMER,
  CUSTOMER_MANAGEMENT_VIEW_CUSTOMER,
  ROLE_MANAGEMENT_ROLE_DETAILS,
  ROLE_MANAGEMENT_ADD_ROLE,
  ROLE_MANAGEMENT_EDIT_ROLE,
  ROLE_MANAGEMENT_EDIT_ROLE_DETAILS,
  COMPANY_STRUCTURE,
  ONBOARDING_QUESTIONS,
  ROPA_DASHBOARD,
  SELECTED_VIEW_GROUP_DETAILS,
  ABOUT_GOTRUST,
  PROFILE,
  CHANGE_PASSWORD,
  SUPPORT,
  CREATE_TICKET,
  VIEW_TICKET_DETAILS,
  EDIT_TICKET_DETAILS,
  ROPA_REGISTER,
  ROPA_BASIC_INFORMATION,
  ROPA_RIVIEW,
  ROPA_ADD_QUESTION,
  DATA_SUBJECT_RIGHTS,
  PRIVACY_POLICY_CREATE_POLICY_TEMPLATE,
  ROPA_VIEW,
  ROPA_VIEW_AUDIT_LOG,
  UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
  UNIVERSAL_CONSENT_MANAGEMENT_FORMCENTRE,
  UNIVERSAL_CONSENT_MANAGEMENT_CONSENTUPLOADS,
  UNIVERSAL_CONSENT_MANAGEMENT_PREFERENCECENTRE,
  UNIVERSAL_CONSENT_ADD_PREFERENCECENTRE_CENTER,
  UNIVERSAL_CONSENT_UPDATE_PREFERENCECENTRE_CENTER,
  UNIVERSAL_CONSENT_CUSTOME_PARAMETERS,
  UNIVERSAL_CONSENT_CONSENT_PURPOSE,
  UCM_PRIVACY_NOTICE,
  UCM_PRIVACY_NOTICE_DETAILS,
  VIEW_UCM_PRIVACY_NOTICE_DETAILS,
  UCM_COLLECTION_TEMPLATE,
  UCM_ADD_COLLECTION_TEMPLATE,
  UCM_ADD_COLLECTION_TEMPLATE_POC,
  UCM_SUBJECT_CONSENT_TYPES,
  UCM_SUBJECT_CONSENT_TYPES_DETAILS,
  UCM_SUBJECT_CONSENT_LIST,
  UCM_SUBJECT_CONSENT_LIST_DETAILS,
  UCM_TEMPLATES,
  UCM_LAB,
  UCM_SOURCE,
  UCM_FORM,
  UCM_SOURCE_CONSENT_UPLOAD,
  UCM_FORM_CREATE,
  UCM_PROCESSING_CATEGORY,
  UCM_PROCESSING_PURPOSE,
  UCM_CONSENT_PURPOSE,
  UCM_PII,
  RETENTION_RULE_LIST,
  DATA_RETENTION,
  RETENTION_RULE_DETAILS,
  RETENTION_RULE_DETAILS_VIEW,
  DATA_RETENTION_DASHBOARD,
  DSR_WORKFLOW_TABLE,
  DSR_ADD_WORKFLOW,
  DSR_VIEW_WORKFLOW,
  DSR_EDIT_WORKFLOW,
  DSR_REQUEST_FORM,
  DSR_FORM_BUILDER,
  DSR_FORM_BUILDER_CREATE_FORM,
  DSR_FORM_BUILDER_VIEW,
  DSR_ADD_QUESTION,
  DSR_FORM_BUILDER_REVIEW,
  DSR_FORM_TRANSLATION,
  DSR_TASK_OVERVIEW,
  DSR_TASK_OVERVIEW_APPROVED,
  DSR_TASK_OVERVIEW_REJECT_IN_PROGRESS,
  DSR_TASK_OVERVIEW_REJECTED,
  DSR_TASK_OVERVIEW_COMPLETED,
  DSR_TASK_OVERVIEW_ARCHIVED,
  DSR_TASK_OVERVIEW_CREATE,
  DSR_MY_REQUEST,
  DSR_MY_REQUEST_DETAILS,
  DSR_ASSIGNEE_VIEW,
  DSR_ASSIGNEE_VIEW_DETAILS,
  DSR_TASK_OVERVIEW_VIEW_PENDING_REQ,
  DSR_TASK_OVERVIEW_VIEW_APPROVED_REQ,
  DSR_TASK_OVERVIEW_VIEW_REJECTED_REQ,
  DSR_TASK_OVERVIEW_VIEW_COMPLETED_REQ,
  DSR_TASK_OVERVIEW_VIEW_REJECTED_IN_PROGRESS,
  DSR_TASK_OVERVIEW_VIEW_ARCHIVED_REQ,
  DSR_EMAIL_TEMPLATES,
  DSR_FORM_REPOSIOTRY,
  DSR_RETENTION_SCHEDULE,
  DSR_TASK_OVERVIEW_VIEW_ASSIGNEE,
  DSR_MY_TASK,
  DSR_MY_TASK_VIEW,
  UCF,
  UCF_IMPROVENENT,
  UCF_ACTIONS,
  UCF_ONBOARDING,
  PRIVACY_OPS,
  PRIVACY_OPS_DOCUMENT_REPO,
  PRIVACY_OPS_REGULATIONS,
  PRIVACY_OPS_REGULATIONS_DETAILS,
  PRIVACY_OPS_RISK_REGISTER,
  PRIVACY_OPS_RISK_REGISTER_DETAILS,
  PRIVACY_OPS_RISK_REGISTER_AUDIT,
  PRIVACY_OPS_ASSESSMENT_REPO,
  PRIVACY_OPS_PROCESSING_ACTIVITIES,
  PRIVACY_OPS_PROCESSING_ACTIVITIES_DETAILS,
  PRIVACY_OPS_ACTIVITIES_DUTIES,
  PRIVACY_OPS_ACTIVITIES_ACTIONS,
  PRIVACY_OPS_ACTIVITIES_IMPROVEMENTS,
  PRIVACY_OPS_COMPLIANCE_DASHBOARD,
  PRIVACY_OPS_RISK_DASHBOARD,
  BREACH_MANAGEMENT,
  BREACH_MANAGEMENT_DASHBOARD,
  BREACH_LIST,
  BREACH_DETAILS,
  ASSESSMENT_DASHBOARD,
  ASSESSMENT_VIEW,
  ASSESSMENT_REVIEW,
  ASSESSMENT_ACTION,
  ASSESSMENT_VIEW_AUDIT_LOG,
  ASSESSMENT_ADD_QUESTION,
  ASSESSMENT_MANAGEMENT_TASK_OVERVIEW,
  ASSESSMENT_MANAGEMENT_TASK_OVERVIEW_AUDIT_LOG,
  ASSESSMENT__TEMPLATES,
  ASSESSMENT__TEMPLATES_VIEW,
  COOKIE_CONSENT_MANAGEMENT,
  COOKIE_CONSENT_DOMAIN,
  COOKIE_CONFIGURATION,
  FILE_CLASSIFICATION,
  PII_ANALYSIS,
  UNSTRUCTURED_DATA_MAPPING,
  PII_LIST,
  VRM_VENDOR_DETAILS,
];
