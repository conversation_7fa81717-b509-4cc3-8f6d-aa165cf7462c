import Cookies from 'js-cookie';
import { debounce } from 'lodash';
import { ChangeEvent, useCallback, useEffect, useMemo, useState } from 'react';
import Modal from 'react-modal';
import { useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { Checkbox } from '../../@/components/ui/checkbox';
import { Label } from '../../@/components/ui/Common/Elements/Label/Label';
import assign from '../../assets/images/assign.png';
import close from '../../assets/images/close.png';
import search from '../../assets/images/search.png';
import { api_key_work as api_key } from '../../utils/helperData';
import TablePaginationDemo from '../common/Pagenation';
import {
  fetchModalDataRoleDetail,
  fetchRoleUsers,
  postRoleAssignment,
} from '../common/services/roleManagement';
import RoleDetailDataTable from './Table/RoleDetailDataTable';

const tableHeadings = [
  { label: 'First Name', width: 'w-[15%]' },
  { label: 'Last Name', width: 'w-[15%]' },
  { label: 'Added Date', width: 'w-[15%]' },
  { label: 'Email', width: 'w-[15%]' },
  { label: 'Role', width: 'w-[15%]' },
  { label: 'Action', width: 'w-[10%]' },
];

const modalHeadings = [
  { label: '', width: 'w-[15%]' },
  { label: 'First Name', width: 'w-[28.34%]' },
  { label: 'Last Name', width: 'w-[28.34%]' },
  { label: 'Email', width: 'w-[28.34%]' },
];

const RoleDetails = () => {
  interface Role {
    role_name: string;
  }

  interface RowData {
    firstName: string;
    lastName: string;
    createdAt: string;
    email: string;
    Role: Role;
    status: string;
    id: number;
  }

  interface ModalData {
    role_id: number;
    firstName: string;
    lastName: string;
    email: string;
    id: number;
  }

  const [tableData, setTableData] = useState<RowData[]>([]);
  console.log('state tableData', tableData);
  const [firstName, setFirstName] = useState<string>('');
  const [lastName, setLastName] = useState<string>('');
  const [status, setStatus] = useState<string>('');
  const [createdAt, setCreatedAt] = useState<string>('');
  const [email, setEmail] = useState<string>('');
  const [role, setRole] = useState<string>('');
  const [name_id, setName_id] = useState<number>(0);

  const [loading, setLoading] = useState(false);

  const loginData = useSelector((state: any) => state.auth.login.login_details);
  const signupData = useSelector((state: any) => state.signup.details);
  let customer_id = 0;
  let access_token: string | null = '';

  if (loginData !== null) {
    access_token = Cookies.get('access_token') || null;
    customer_id = loginData.customer_id;
  }
  if (signupData !== null) {
    access_token = Cookies.get('access_token') || null;
    customer_id = signupData.sign_up_details.customer_id;
  }
  console.log('access_token:', access_token);
  console.log('customer_id:', customer_id);

  const navigate = useNavigate();

  const [UserIsOpen, setUserIsOpen] = useState(false);
  console.log('UserIsOpen', UserIsOpen);

  const location = useLocation();
  const roleId = location.state?.id;
  console.log('role id', roleId);

  /////////////pagination////////////////

  const [count, setCount] = useState(0);
  const [numberOfItems, setNumberOfItems] = useState(10);
  const [page, setPage] = useState(1);

  function handleNumberOfPages(value: number) {
    setNumberOfItems(value);
    setTriggerFetch(true);
  }

  function handlePageChange(value: number) {
    setPage(value);
    setTriggerFetch(true);
  }
  const [triggerFetch, setTriggerFetch] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        const data = await fetchRoleUsers(roleId, numberOfItems, page);

        if (data.result) {
          console.log('Assign Data:', tableData);
          setTableData(data.result.rows);
          console.log('after Assign Data:', tableData);
          setCount(data.result.count);
        } else {
          console.error('Error: data.result.rows is undefined');
        }
      } catch (error) {
        console.error('Error:', error);
      }
      setLoading(false);
    };
    if (triggerFetch) {
      fetchData();
      setTriggerFetch(false);
    }
  }, [
    firstName,
    lastName,
    createdAt,
    email,
    role,
    status,
    name_id,
    triggerFetch,
    page,
    numberOfItems,
  ]);

  useEffect(() => {
    if (!UserIsOpen) {
      console.log('fetching data');
      setTriggerFetch(true);
      setUserIsOpen(false);
    }
  }, [UserIsOpen]);
  ////modal pagination
  const [modalCount, setModalCount] = useState(0);
  const [modalnumOfItems, setModalNumberOfItems] = useState(10);
  const [modalpage, setModalPage] = useState(1);

  function handleNumberOfPagesModal(value: number) {
    setModalNumberOfItems(value);
    setTriggerFetchModal(true);
  }

  function handlePageChangeModal(value: number) {
    setModalPage(value);
    setTriggerFetchModal(true);
  }

  const [triggerFetchModal, setTriggerFetchModal] = useState(false);
  ///////////////////search modal
  const [searchTerm, setSearchTerm] = useState('');

  ////////////////////modal fetch

  const [modalData, setmodalData] = useState<ModalData[]>([]);

  const handlCloseModal = () => {
    setUserIsOpen(false);
    setSearchTerm('');
  };

  const fetchModalData = useCallback(
    debounce(async (searchTerm: string) => {
      setLoading(true);
      try {
        const data = await fetchModalDataRoleDetail(
          customer_id,
          modalnumOfItems,
          modalpage,
          searchTerm
        );

        if (data.result) {
          setmodalData(data.result.rows);
          setModalCount(data.result.count);
        } else {
          console.error('Error: data.result.rows is undefined');
        }
      } catch (error) {
        console.error('Error:', error);
      }
      setLoading(false);
    }, 100),
    [customer_id, modalnumOfItems, modalpage, api_key, access_token]
  );

  useEffect(() => {
    if (triggerFetchModal) {
      fetchModalData(searchTerm);
      setTriggerFetchModal(false);
    }
  }, [triggerFetchModal, fetchModalData, modalnumOfItems, modalpage]);

  ///////////////search modal

  const updateSearchTerm = useCallback(
    debounce((event: ChangeEvent<HTMLInputElement>) => {
      setSearchTerm(event.target.value);
      fetchModalData(event.target.value);
    }, 100), // delay in ms
    []
  );

  const filteredTableData = useMemo(() => {
    if (!modalData) {
      return [];
    }

    return modalData.filter((row) =>
      row.firstName.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [modalData]);

  const [checkedItems, setCheckedItems] = useState<number[]>([]);
  const [uncheckedItems, setUncheckedItems] = useState<number[]>([]);

  const handleCheckboxChange = (e: any, id: any, role_id: any) => {
    if (e.target.checked) {
      setCheckedItems([...checkedItems, id]);
      setUncheckedItems(uncheckedItems.filter((itemId) => itemId !== id));
    } else {
      setCheckedItems(checkedItems.filter((itemId) => itemId !== id));
      if (roleId === role_id) {
        setUncheckedItems([...uncheckedItems, id]);
      }
    }
  };

  const postData = async () => {
    setLoading(true);
    try {
      const data = await postRoleAssignment(checkedItems, uncheckedItems, roleId);
      console.log(data);
      setTriggerFetch(true);
    } catch (error) {
      console.error('Error posting data:', error);
    }
    setLoading(false);
  };

  useEffect(() => {
    const checkedIds = modalData.filter((item) => item.role_id === roleId).map((item) => item.id);
    setCheckedItems(checkedIds);
  }, [modalData, roleId]);

  return (
    <>
      {/* <div
      className="flex flex-col bg-[#FBFBFB] font-[Poppins]"
      style={{ height: "calc(100% - 90px)" }}
    >
      <div className="w-full h-full flex flex-col bg-[#FBFBFB] font-[Poppins]">
        <div className="flex-grow"> */}
      <div className="size-full px-5">
        {/* Your main content goes here */}
        {/* <div className="flex flex-row items-center h-12 justify-end">
          <div className="flex flex-row gap-[10px] justify-center items-center">
                <button onClick={() => navigate(-1)}>
                  <img
                    src={rightArrow}
                    alt="right arrow sign"
                    className="h-6 w-6 shrink-0 bg-black rounded-full"
                  />
                </button>
                <span className="text-black font-[Poppins] text-xl font-semibold leading-normal">
                  Role Management
                </span>
              </div>
        </div> */}

        <div className="w-full rounded-md bg-white font-[Poppins]">
          <div className="items-flex-end flex flex-col gap-4 p-5">
            <div className="flex-start flex h-11 w-full items-center justify-between self-stretch">
              <div className="flex flex-row justify-start gap-[31px] pl-2">
                <div className="flex flex-row items-center justify-between gap-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="8"
                    height="8"
                    viewBox="0 0 8 8"
                    fill="none"
                  >
                    <circle cx="3.99537" cy="3.74537" r="3.49537" fill="#00CBA0" />
                  </svg>
                  <Label className="text-[10px] leading-[13.981px] text-[#262626]">
                    Active users
                  </Label>
                </div>

                <div className="flex flex-row items-center justify-between gap-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="8"
                    height="8"
                    viewBox="0 0 8 8"
                    fill="none"
                  >
                    <circle cx="3.99537" cy="3.74537" r="3.49537" fill="#FF9950" />
                  </svg>
                  <Label className="text-[10px] leading-[13.981px] text-[#262626]">
                    Inactive users
                  </Label>
                </div>

                <div className="flex flex-row items-center justify-between gap-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="8"
                    height="8"
                    viewBox="0 0 8 8"
                    fill="none"
                  >
                    <circle cx="3.99537" cy="3.74537" r="3.49537" fill="#FA1464" />
                  </svg>
                  <Label className="text-[10px] leading-[13.981px] text-[#262626]">
                    Archived users
                  </Label>
                </div>
              </div>
              <div>
                <button
                  onClick={() => {
                    setUserIsOpen(true);
                    fetchModalData('');
                  }}
                  className="flex h-11 items-center justify-center rounded-lg bg-custom-primary px-4 py-3"
                >
                  <div className="flex items-center justify-center gap-2">
                    <img src={assign} alt="right arrow sign" className="relative size-4 shrink-0" />
                    <span className="font-[Poppins] text-sm font-medium leading-[29px] text-white">
                      Assign User
                    </span>
                  </div>
                </button>
              </div>
            </div>

            {/* <div className="flex flex-row justify-end gap-[31px]">
              <div className="flex flex-row justify-between gap-1 items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="8"
                  height="8"
                  viewBox="0 0 8 8"
                  fill="none"
                >
                  <circle
                    cx="3.99537"
                    cy="3.74537"
                    r="3.49537"
                    fill="#00CBA0"
                  />
                </svg>
                <span className="text-[#262626] font-[Poppins] text-[10px] font-medium leading-[13.981px]">
                  Active users
                </span>
              </div>

              <div className="flex flex-row justify-between gap-1 items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="8"
                  height="8"
                  viewBox="0 0 8 8"
                  fill="none"
                >
                  <circle
                    cx="3.99537"
                    cy="3.74537"
                    r="3.49537"
                    fill="#FF9950"
                  />
                </svg>
                <span className="text-[#262626] font-[Poppins] text-[10px] font-medium leading-[13.981px]">
                  Inactive users
                </span>
              </div>

              <div className="flex flex-row justify-between gap-1 items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="8"
                  height="8"
                  viewBox="0 0 8 8"
                  fill="none"
                >
                  <circle
                    cx="3.99537"
                    cy="3.74537"
                    r="3.49537"
                    fill="#FA1464"
                  />
                </svg>
                <span className="text-[#262626] font-[Poppins] text-[10px] font-medium leading-[13.981px]">
                  Archived users
                </span>
              </div>
            </div> */}

            <div
              className="table_main_content mt-0 w-full overflow-auto bg-white"
              style={{ height: 'calc(100vh - 21.5rem)' }}
            >
              {/* <RoleDetailTable
                tableHeadings={tableHeadings}
                rowData={tableData}
                loading={loading}
              /> */}
              <RoleDetailDataTable
                tableHeadings={tableHeadings}
                data={tableData}
                loading={loading}
              />
            </div>
          </div>
          <div className="flex flex-row items-center justify-between">
            <TablePaginationDemo
              count={count}
              handleItemsChange={handleNumberOfPages}
              handlePageChange={handlePageChange}
              currentPage={1}
              numOfItems={10}
            />
          </div>
        </div>
      </div>
      {/* {loading && (
        <div className="bg-black opacity-70 fixed top-0 left-0 h-screen w-screen">
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              height: "100vh",
            }}
            role="status"
          >
            <svg
              aria-hidden="true"
              className="w-8 h-8 text-gray-200 animate-spin dark:text-gray-600 fill-blue-600"
              viewBox="0 0 100 101"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
                fill="currentColor"
              />
              <path
                d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
                fill="currentFill"
              />
            </svg>
            <span className="sr-only">Loading...</span>
          </div>
        </div>
      )} */}
      <Modal
        isOpen={UserIsOpen}
        onRequestClose={() => setUserIsOpen(false)}
        shouldCloseOnOverlayClick={false}
        style={{
          overlay: {
            backgroundColor: 'rgba(0, 0, 0, 0.5)', // This is black with 50% opacity
          },
          content: {
            width: '80%',
            height: '95%',
            maxWidth: '600px',
            maxHeight: '600px',
            margin: 'auto',
            overflow: 'auto',
          },
        }}
      >
        <div className="flex max-w-[639px] flex-col rounded bg-white p-5">
          <div className="flex justify-between gap-5 rounded bg-white p-2.5 text-xl font-medium leading-6 text-gray-700 max-md:max-w-full max-md:flex-wrap">
            <div className="flex-auto font-semibold">User list</div>
            {/* <button onClick={() => setUserIsOpen(false)}> */}
            <button onClick={handlCloseModal}>
              <img
                loading="lazy"
                src={close}
                className="aspect-square w-6 shrink-0 border border-solid border-slate-100"
                title="Cross icon"
              />
            </button>
          </div>
          <div className="mt-4 flex w-full justify-between gap-5 px-px text-xs max-md:max-w-full max-md:flex-wrap">
            <div className="flex justify-between gap-5 rounded-md border border-solid border-stone-300 bg-white px-2.5 py-3 text-gray-400">
              <input
                type="text"
                placeholder="Search"
                className="bg-transparent font-[Poppins] text-[13px] font-normal leading-normal text-[#B5B5C3] outline-none"
                onChange={updateSearchTerm}
              />
              <img src={search} alt="search sign" className="size-[19px] shrink-0" />
            </div>
            <div className="flex">
              <button
                onClick={() => {
                  setUserIsOpen(false);
                  postData();
                }}
                className="flex h-9 w-[150px] items-center justify-center rounded-md bg-custom-primary p-[2px]"
              >
                <div className="flex items-center justify-center gap-[7px]">
                  <span className="font-[Poppins] text-xs leading-[20px] text-white">
                    (Un)Assign
                  </span>
                </div>
              </button>
            </div>
          </div>
          <br />
          <div className="mt-0 p-0">
            <table className="mt-0 w-full min-w-max table-auto text-left">
              <thead className="">
                <tr className="flex h-11 w-full flex-row items-center gap-8 rounded-[3px] bg-[#E1E1E1] py-2.5 pl-6 pr-2.5">
                  {modalHeadings.map((item) => {
                    const cssClass =
                      'flex cursor-pointer transition-colors items-center ' + item.width;
                    return (
                      <th className={cssClass}>
                        <span className="capitialize shrink-0 font-[Poppins] text-xs font-medium leading-[11.185px] tracking-[0.72px] text-quaternary-text">
                          {item.label}
                        </span>
                      </th>
                    );
                  })}
                </tr>
              </thead>

              <tbody className="">
                {filteredTableData.map((item) => {
                  return (
                    console.log('modalData', modalData),
                    (
                      <tr className="flex h-8 w-full items-center gap-8 border-[0.6px] border-solid border-[#E0E3E7] px-2.5 py-1">
                        <td className="w-[10%]">
                          <div className="] flex shrink-0 flex-row items-center gap-1">
                            <Checkbox
                              checked={checkedItems.includes(item.id)} // change this line
                              onChange={(e) => handleCheckboxChange(e, item.id, item.role_id)}
                              className="size-4 shrink-0 rounded-md border-gray-900/20 transition-all checked:border-[#FCAB10] checked:bg-[#FCAB10] hover:scale-105 hover:before:opacity-0"
                            />
                          </div>
                        </td>
                        <td className="w-1/5">
                          <div className="flex items-center">
                            <div className="flex shrink-0 flex-row items-center gap-1">
                              <p className="ml-7 shrink-0 font-[Poppins] text-[10px] font-medium leading-[13.981px] text-[#262626]">
                                {item.firstName} Hello
                              </p>
                            </div>
                          </div>
                        </td>
                        <td className="w-[25%]">
                          <div className="flex items-center">
                            <p className="ml-10 shrink-0 font-[Poppins] text-[10px] font-medium leading-[13.981px] text-[#262626]">
                              {item.lastName}
                            </p>
                          </div>
                        </td>
                        <td className="w-[35%]">
                          <div className="flex items-center">
                            <div className="flex shrink-0 flex-row items-center gap-1">
                              <p className="shrink-0 font-[Poppins] text-[10px] font-medium leading-[13.981px] text-[#262626]">
                                {item.email}
                              </p>
                            </div>
                          </div>
                        </td>
                      </tr>
                    )
                  );
                })}
              </tbody>
            </table>
          </div>
          <TablePaginationDemo
            count={modalCount}
            handleItemsChange={handleNumberOfPagesModal}
            handlePageChange={handlePageChangeModal}
            currentPage={1}
            numOfItems={10}
          />
        </div>
      </Modal>
      {/* </div>
      </div>
    </div> */}
    </>
  );
};

export default RoleDetails;
