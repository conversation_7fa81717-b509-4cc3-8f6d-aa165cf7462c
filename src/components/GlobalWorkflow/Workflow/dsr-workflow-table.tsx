import { ColumnDef } from '@tanstack/react-table';
import { debounce } from 'lodash';
import { ArrowUpDown } from 'lucide-react';
import { ChangeEvent, useCallback, useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { Button } from '../../../@/components/ui/Common/Elements/Button/Button';
import { ImageButton } from '../../../@/components/ui/Common/Elements/Button/ImageButton';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '../../../@/components/ui/Common/Elements/Dialog/Dialog';
import { Label } from '../../../@/components/ui/Common/Elements/Label/Label';
import { Badge } from '../../../@/components/ui/badge';
import solidEye from '../../../assets/IconoirEyeSolid.svg';
import search from '../../../assets/Search.svg';
import edit from '../../../assets/images/edit_new.png';
import addSign from '../../../assets/plusSign.svg';
import { setDsrWorkflowStep } from '../../../redux/reducers/GlobalWorkflow/globalworkflow-slice.ts';
import { getInitialsByName } from '../../../utils/helperData';
import { GLOBAL_EDIT_WORKFLOW, GLOBAL_VIEW_WORKFLOW } from '../../../utils/routeConstant';
import AvatarFrame from '../../common/Avatar';
import { convertDateToHumanView } from '../../common/CommonHelperFunctions';
import {
  addWorkflow,
  fetchWorkflowList,
  fetchWorkflowListById,
} from '../../common/services/globalWorkflowApis';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../../../@/components/ui/Common/Elements/Form/Form';
import { Input } from '../../../@/components/ui/Common/Elements/Input/Input';

import { Tooltip } from 'react-tooltip';
import SearchableSelect from '../../../@/components/ui/Common/Elements/Select/SearchableSelect';
import { fetchEntities } from '../../../redux/actions/ActivityLog/ActivityLogActions';
import { getRegulationsList } from '../../../redux/actions/CustomerManagement/CustomerManagementActions';
import { AppDispatch, RootState } from '../../../redux/store';
import {
  DsrWorkflowTableItem,
  DsrWorkflowTableProp,
  WORKFLOW_MODULES,
} from '../../../types/global-workflow';
import DynamicTable from '../../common/ShadcnDynamicTable/dynamic-table';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../@/components/ui/Common/Elements/Select/Select';
const formSchema = z.object({
  flowtype: z
    .string()
    .min(1, 'This field is required')
    .regex(
      /^[A-Za-z][A-Za-z0-9_()-]*[\sA-Za-z0-9_\()-]*$/,
      'Must start with a letter and can include letters, parentheses, numbers, spaces, hyphens, and underscores'
    ),
  group_id: z.string().min(1, 'This field is required'),
  module_name: z.string().min(1, 'This field is required'),
  // regulation_id: z.array(z.number()).optional(),
});

const DsrWorkflowTable = () => {
  const dispatch = useDispatch<AppDispatch>();
  const loginData = useSelector((state: RootState) => state.auth.login.login_details);
  const navigate = useNavigate();
  const [searchValue, setSearchValue] = useState('');
  const [workflowListData, setWorkflowListData] = useState<DsrWorkflowTableItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newWorkflowName, setNewWorkflowName] = useState('');
  const [flowTypeSuggestions, setFlowTypeSuggestions] = useState<string[]>([]);
  const [page, setPage] = useState(1);
  const [size, setSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const { t } = useTranslation();
  const { entities, selectedEntityId } = useSelector((state: RootState) => state.activityLog);
  const [customerRegulationsList, setCustomerRegulationsList] = useState<any[]>([]);

  //   const count = useSelector(
  //   (state: RootState) => state.globalWorkflow?.workflowPagination.count
  // );
  // const currentPage = useSelector(
  //   (state: RootState) => state.globalWorkflow?.workflowPagination.page
  // );
  // const numberOfItems = useSelector(
  //   (state: RootState) => state.globalWorkflow?.workflowPagination.itemsPerPage
  // );

  // function handleNumberOfPages(value: number) {
  //   dispatch(setDsrWorkflowPaginationItemsPerPage(value));
  // }

  // function handlePageChange(value: number) {
  //   dispatch(setDsrWorkflowPaginationPage(value));
  // }

  const form = useForm<DsrWorkflowTableProp>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      flowtype: '',
      group_id: '',
      module_name: 'DSR',
    },
  });
  const customer_id = loginData?.customer_id ?? 0;

  const fetchData = async (searchTerm?: string) => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetchWorkflowList(page, size, searchTerm);
      if (response?.data.success) {
        setWorkflowListData(response?.data.result.rows);
        setTotalCount(response?.data.result.count);
      } else {
        setError('Failed to fetch workflow list');
      }
    } catch {
      toast.error(t('FrontEndErrorMessage.ApiErrors.AnErrorOccurred'));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData(searchValue);
  }, [page, size, searchValue]);

  useEffect(() => {
    dispatch(fetchEntities(customer_id, selectedEntityId));
  }, [dispatch, customer_id, selectedEntityId]);

  useEffect(() => {
    async function fetchRegulationList() {
      try {
        const data = await getRegulationsList();
        if (data && Array.isArray(data.rows)) {
          setCustomerRegulationsList(data.rows);
        } else {
          console.error('Expected "rows" to be an array in the API response.');
        }
      } catch (error) {
        console.error('Error fetching customer regulations list:', error);
      }
    }
    fetchRegulationList();
  }, []);

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handlePageSizeChange = (newSize: number) => {
    setSize(newSize);
    setPage(1);
  };

  const filterFlowTypes = useCallback(
    debounce((input: string) => {
      if (!input) {
        setFlowTypeSuggestions([]);
        return;
      }

      const filteredSuggestions = workflowListData
        .map((workflow) => workflow.flowtype)
        .filter((flowtype) => flowtype.toLowerCase().includes(input.toLowerCase()))
        .filter((value, index, self) => self.indexOf(value) === index);

      setFlowTypeSuggestions(filteredSuggestions);
    }, 300),
    [workflowListData]
  );

  // Modal Submit function
  const [isLoading, setIsLoading] = useState(false);

  const handleAddWorkflow = async () => {
    const newWorkflowName = form.getValues('flowtype');
    const selectedGroupId = form.getValues('group_id');
    const module_name = form.getValues('module_name');
    if (!newWorkflowName.trim()) {
      toast.error(t('FrontEndErrorMessage.DSR.PleaseEnterWorkflowType'));
      return;
    }

    const existingFlowType = workflowListData.some(
      (workflow) =>
        workflow.flowtype.toLowerCase() === newWorkflowName.toLowerCase() &&
        String(workflow.group_id) === String(selectedGroupId)
    );

    if (existingFlowType) {
      toast.error(t('FrontEndErrorMessage.DSR.DuplicateWorkflow'));
      return;
    }

    setIsLoading(true); // Disable the button
    toast.loading(t('FrontEndErrorMessage.DSR.AddingWorkflow'));

    try {
      const response = await addWorkflow(newWorkflowName, selectedGroupId, module_name);
      if (response.success) {
        toast.dismiss();
        toast.success(t('FrontEndErrorMessage.DSR.WorkflowAddedSuccessfully'));
        setIsModalOpen(false);
        fetchWorkflowDetailById(response.result?.id, 'draft', 'edit');
      }
    } catch (error) {
      toast.dismiss();
      toast.error(t('FrontEndErrorMessage.DSR.FailedToAddWorkflow'));
    } finally {
      setIsLoading(false); // Enable the button again
    }
  };
  // Get workflow by id
  const fetchWorkflowDetailById = async (
    id: number,
    workflow_status?: string,
    eventType?: string,
    group_id?: string,
    module_name?: string
  ) => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetchWorkflowListById(id);
      console.log('testttt1111', response);
      if (response?.data.success) {
        dispatch(setDsrWorkflowStep(response?.data.result));
        if (eventType == 'edit')
          navigate(GLOBAL_EDIT_WORKFLOW, {
            state: {
              id: id,
              workFlow: workflow_status,
              group_id: group_id || response?.data.result.group_id,
            },
          });
        else
          navigate(GLOBAL_VIEW_WORKFLOW, {
            state: {
              name: response?.data.result.flowtype,
              group_id: group_id || response?.data.result.group_id,
            },
          });
      } else {
        setError('Failed to fetch workflow list');
      }
    } catch {
      toast.error(t('FrontEndErrorMessage.ApiErrors.AnErrorOccurred'));
    } finally {
      setLoading(false);
    }
  };

  //////////////////////
  // Update workflow
  const handleUpdateRequest = async (
    id: number,
    type: string,
    workflow_status: string,
    eventType: string,
    group_id: string,
    module_name: string
  ) => {
    fetchWorkflowDetailById(id, workflow_status, eventType, group_id, module_name);
  };

  const handleSearchValue = useCallback(
    debounce((event: ChangeEvent<HTMLInputElement>) => {
      setSearchValue(event.target.value);
      setPage(1);
    }, 1000),
    []
  );

  const workflowTableColumns: ColumnDef<DsrWorkflowTableItem>[] = [
    {
      accessorKey: 'flowtype',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('DSR.AssigneeModal.FlowType')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => <div>{row.getValue('flowtype') || '-'}</div>,
    },
    {
      accessorKey: 'module_name',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('Module')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => <div>{row.getValue('module_name') || '-'}</div>,
    },
    {
      accessorKey: 'created_by',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('RoleManagement.ViewRole.CreatedBy')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const fullName = `${row.original.firstName} ${row.original.lastName}`;
        return (
          <div className="flex flex-row items-center gap-2 whitespace-nowrap">
            <AvatarFrame value={fullName} getInitials={getInitialsByName} />
            {fullName}
          </div>
        );
      },
    },
    {
      accessorKey: 'business_unit',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          Business Unit
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const group = row.original.Group;
        return <div>{group ? `${group.name}` : '-'}</div>;
      },
    },
    {
      accessorKey: 'createdAt',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('UserManagement.ViewUser.CreatedDate')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => <div>{convertDateToHumanView(row.getValue('createdAt'))}</div>,
    },
    {
      accessorKey: 'workflow_status',
      header: ({ column }) => (
        <Button
          variant="ghost"
          className="p-0"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
        >
          {t('Ropa.Activity.TableHeading.Status')}
          <ArrowUpDown className="ml-2 size-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const status = row.getValue('workflow_status') as string;
        const bgColor =
          status === 'draft' ? '#CBD5E1' : status === 'published' ? '#2DB078' : 'transparent';
        const textColor = status === 'published' ? '#ffffff' : 'inherit';

        return (
          <Badge
            className="justify-center text-center"
            style={{
              backgroundColor: bgColor,
              color: textColor,
              textTransform: 'capitalize',
            }}
          >
            {status || '-'}
          </Badge>
        );
      },
    },
    {
      accessorKey: 'action',
      header: () => (
        <Button variant="ghost" className="p-0">
          {t('Ropa.Activity.TableHeading.Action')}
        </Button>
      ),
      cell: ({ row }) => (
        <div className="flex flex-row gap-2">
          <Button
            id="view"
            variant="default"
            className="w-8 p-0"
            onClick={() =>
              // navigate(DSR_VIEW_WORKFLOW, {
              //   state: { name: row.original.flowtype },
              // })
              handleUpdateRequest(
                row?.original.id,
                row.original.flowtype,
                row.getValue('workflow_status'),
                'view',
                row.original.group_id,
                row.original.module_name
              )
            }
          >
            {/* <img src={review} alt="View" className="aspect-square h-6" /> */}
            <img className="size-5" src={solidEye} alt="eye icon" />
            <Tooltip
              anchorSelect={`#view`}
              place="left"
              style={{
                color: '#FFF',
                fontFamily: 'Poppins',
                fontSize: '14px',
                fontStyle: 'normal',
                fontWeight: '400',
                lineHeight: 'normal',
                background: '#0B101C',
                borderRadius: '8px',
                opacity: '0.8',
                display: 'inline-flex',
                padding: '9px 16px',
                justifyContent: 'flex-start',
                alignItems: 'center',
                gap: '10px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
              }}
              content={'View'}
            />
          </Button>
          <Button
            id="edit"
            variant="default"
            className="w-8 p-0"
            onClick={
              () =>
                handleUpdateRequest(
                  row?.original.id,
                  row.original.flowtype,
                  row.getValue('workflow_status'),
                  'edit',
                  row.original.group_id,
                  row.original.module_name
                )
              // navigate(DSR_EDIT_WORKFLOW, {
              //   state: { id: row.original.id, name: row.original.flowtype },
              // })
            }
          >
            <img src={edit} alt="Edit" className="aspect-square h-6" />
            <Tooltip
              anchorSelect={`#edit`}
              place="right"
              style={{
                color: '#FFF',
                fontFamily: 'Poppins',
                fontSize: '14px',
                fontStyle: 'normal',
                fontWeight: '400',
                lineHeight: 'normal',
                background: '#0B101C',
                borderRadius: '8px',
                opacity: '0.8',
                display: 'inline-flex',
                padding: '9px 16px',
                justifyContent: 'flex-start',
                alignItems: 'center',
                gap: '10px',
                whiteSpace: 'nowrap',
                overflow: 'hidden',
              }}
              content={'Edit'}
            />
          </Button>
        </div>
      ),
    },
  ];

  const openModal = () => {
    setNewWorkflowName('');
    setSearchValue('');
    setIsModalOpen(true);
  };

  useEffect(() => {
    if (!isModalOpen) {
      form.reset();
    }
  }, [isModalOpen]);

  return (
    <div className="size-full">
      <div className="w-full rounded-md bg-white font-[Poppins]">
        <div className="items-flex-end flex flex-col gap-4 p-5">
          <div className="flex h-9 flex-row items-center justify-between gap-5">
            <div>
              <Label className="text-xl font-semibold">{t('DSR.WorkFlow.WorkFlow')}</Label>
            </div>
            <div className="flex flex-row items-center gap-5">
              <div className="flex h-11 w-[222px] items-center justify-between rounded-md border border-solid border-input px-3">
                <img src={search} alt="search sign" className="size-[19px]" />
                <input
                  type="text"
                  placeholder={t('Common.Search')}
                  className="ml-1 h-auto w-full outline-none"
                  onChange={handleSearchValue}
                />
              </div>
              <ImageButton
                className="h-11 bg-custom-primary hover:bg-custom-primary"
                onClick={openModal}
              >
                <img src={addSign} alt="plussign" />
                <p className="text-primary-background"> {t('DSR.WorkFlow.AddWorkflow')}</p>
              </ImageButton>
            </div>
          </div>
        </div>
        <div></div>
      </div>
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('DSR.WorkFlow.AddWorkflow')}</DialogTitle>
          </DialogHeader>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleAddWorkflow)}>
              <FormField
                control={form.control}
                name="flowtype"
                render={({ field }) => (
                  <FormItem className="pb-2">
                    <FormLabel>
                      <span>Enter workflow name</span>
                      <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input {...field} placeholder={t('DSR.WorkFlow.EnterFlowType')} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="module_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>{t('Module')}</FormLabel>
                    <FormControl>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                        // disabled={isFormLoading}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder={t('Select module')} />
                        </SelectTrigger>
                        <SelectContent>
                          {WORKFLOW_MODULES.map((module) => (
                            <SelectItem key={module.value} value={module.value}>
                              {t(module.label)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="group_id"
                render={({ field }) => (
                  <FormItem className="pb-2">
                    <FormLabel>
                      Business Unit
                      <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <SearchableSelect
                        placeholder="Select business unit"
                        options={
                          entities?.map((entity) => ({ value: entity?.name, id: entity?.id })) || []
                        }
                        value={field.value ? String(field.value) : ''}
                        onChange={field.onChange}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {/* <FormField
control={form.control}
name="regulation_id"
render={({ field }) => (
<FormItem className="pb-2">
<FormLabel>
{t('DSR.FormBuilder.Regulations')}
<span className="text-red-500">*</span>
</FormLabel>
<FormControl>
<Select
onValueChange={(value) => {
const selectedValues = value.split(',').map(Number);
field.onChange(selectedValues);
}}
value={field.value ? field.value.join(',') : ''}
>
<SelectTrigger>
<SelectValue placeholder={t('DSR.WorkFlow.SelectRegulations')} />
</SelectTrigger>
<SelectContent className="max-w-[50%]">
{customerRegulationsList.map((entity) => (
<SelectItem key={entity.id} value={String(entity.id)}>
{entity.authoritative_source}
</SelectItem>
))}
</SelectContent>
</Select>
</FormControl>
<FormMessage />
</FormItem>
)}
/> */}
              <DialogFooter className="mt-4 flex justify-end">
                <Button type="button" variant="outline" onClick={() => setIsModalOpen(false)}>
                  {t('Common.Cancel')}
                </Button>
                <Button
                  type="submit"
                  className="bg-custom-primary text-white hover:bg-custom-primary"
                >
                  {t('Common.Add')}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
      <div className="bottom-0 bg-white">
        {/* <TablePaginationDemo
          count={totalCount}
          currentPage={page}
          numOfItems={size}
          handlePageChange={handlePageChange}
          handleItemsChange={handlePageChange}
        /> */}
        <div
          style={{ height: 'calc(100vh - 13.5rem)' }}
          className="table_main_content mt-0 w-full overflow-auto"
        >
          <DynamicTable
            data={workflowListData}
            columns={workflowTableColumns}
            loading={loading}
            enablePagination={false}
            enableSorting
            ServerSidePaginationDetails={{
              totalRecords: totalCount,
              currentPage: page,
              currentSize: size,
              handlePageSizeChange: handlePageSizeChange,
              handlePageNumberChange: handlePageChange,
            }}
          />
        </div>
      </div>
    </div>
  );
};

export default DsrWorkflowTable;
