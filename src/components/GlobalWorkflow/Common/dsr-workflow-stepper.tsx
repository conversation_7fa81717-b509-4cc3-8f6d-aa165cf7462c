import { Plus } from 'lucide-react';
import React, { useEffect, useState } from 'react';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button } from '../../../@/components/ui/Common/Elements/Button/Button';
import {
  setDsrSelectedWorkflowStep,
  setDsrWorkflowStep,
} from '../../../redux/reducers/GlobalWorkflow/globalworkflow-slice.ts';
import { RootState } from '../../../redux/store';
import {
  ApiError,
  DsrEditWorkflowActionsProperties,
  DsrEditWorkflowStepperProperties,
  WorkflowInterface,
} from '../../../types/global-workflow';
import { GLOBAL_WORKFLOW_TABLE } from '../../../utils/routeConstant';
import { createWorkflowSteps, editWorkflow } from '../../common/services/globalWorkflowApis';
import Modal from '../../UI/Modal';
import AddDsrWorkflowTasks from '../Workflow/AddWorkflow/tasks';
import { CustomStepper, useStepper } from './index';
import AddWorkflowModal from './Modal/add-workflow-modal';
import ConfirmModal from './Modal/confirm-modal';
import { dsrEditVerifyWorkflowTableData } from './rendering-data';
import { Step } from './step';

const steps = [{ id: '0', step_title: 'Add Step', label: 'Add Step', description: '' }];

const viewDsrSteps: WorkflowInterface = {
  id: 1,
  flowtype: '',
  module_name: '',
  created_by: '',
  created_date: '',
  workflow: '',
  CommonWorkflowSteps: [
    { id: '1', step_title: '', label: 'Verify', description: '', activepieces_automation_id: '' },
    {
      id: '1',
      step_title: '',
      label: 'Acknowledgement',
      description: '',
      activepieces_automation_id: '',
    },
    {
      id: '1',
      step_title: '',
      label: 'Data Gathering',
      description: '',
      activepieces_automation_id: '',
    },
    {
      id: '1',
      step_title: '',
      label: 'Data Cleansing',
      description: '',
      activepieces_automation_id: '',
    },
    {
      id: '1',
      step_title: '',
      label: 'Quality Check',
      description: '',
      activepieces_automation_id: '',
    },
    {
      id: '1',
      step_title: '',
      label: 'Final Response',
      description: '',
      activepieces_automation_id: '',
    },
  ],
  group_id: '',
};

export function DsrEditWorkflowStepper({ isEditMode, workFlow }: DsrEditWorkflowStepperProperties) {
  const dispatch = useDispatch();
  const { prevStep, resetSteps, isDisabledStep, hasCompletedAllSteps, isLastStep, nextStep } =
    useStepper();
  const allSteps = useSelector((state: RootState) => state?.globalWorkflow?.workflow);
  const workflowId = useSelector((state: RootState) => state?.globalWorkflow?.workflow.id);
  const activeStep = useSelector((state: RootState) => state?.globalWorkflow?.selectedWorkflowStep);
  console.log('activeStepglobal', activeStep, allSteps);

  const [addWorkFlowStatus, setaddWorkFlowStatus] = useState(false);
  const [addStepStatus, setAddStepStatus] = useState(false);
  const [getAllEditSteps, setAllEditSteps] = useState(allSteps);
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const [stepperData, setStepperData] = useState(isEditMode ? allSteps : viewDsrSteps);

  // Calculate actual steps length excluding the "Add Step"
  const getActualStepsLength = () => {
    return (
      stepperData?.CommonWorkflowSteps?.filter((step) => step.label !== 'Add Step').length || 0
    );
  };

  const handleAddStep = (value: number) => {
    if (stepperData && stepperData?.CommonWorkflowSteps.length - 1 == value && isEditMode) {
      setaddWorkFlowStatus(true);
    }
  };

  const handleAddNewStep = async (value: any) => {
    const data = {
      type_id: workflowId,
      step_title: value?.label,
      guidance_text: value?.description,
      label: value?.label,
    };
    const isDuplicate = stepperData?.CommonWorkflowSteps?.some(
      (step) => step.label === value?.label || step.step_title === value?.label
    );

    if (isDuplicate) {
      toast.error(t('FrontEndErrorMessage.DSR.DuplicateStepsChooseDifferentName'));
      return;
    }
    toast.loading(t('CommonErrorMessages.AddingWorkflowNewStep'));
    try {
      const response = await createWorkflowSteps(data);
      if (response?.success) {
        toast.dismiss();
        toast.success(t('ToastMessages.Workflow.StepAddedSuccessfully'));
        const valueToStore = {
          ...value,
          id: response.result.id,
          step_title: value.label,
          label: value.label,
          description: '',
          activepieces_automation_id: '',
        };
        dispatch(
          setDsrWorkflowStep({
            ...stepperData,
            CommonWorkflowSteps: [
              ...(stepperData?.CommonWorkflowSteps.slice(0, -1) || []),
              valueToStore,
            ],
          })
        );
        setStepperData({
          ...stepperData,
          CommonWorkflowSteps: [
            ...(stepperData?.CommonWorkflowSteps.slice(0, -1) || []),
            valueToStore,
            stepperData?.CommonWorkflowSteps.at(-1),
          ],
        });
        setIsModalOpen(false);
        setAddStepStatus(!addStepStatus);
      } else {
        toast.dismiss();
        toast.error(t('FrontEndErrorMessage.DSR.UserUnauthaorized'));
      }
    } catch (error) {
      toast.dismiss();
      toast.error(t('ToastMessages.General.ErrorWhileAddingWorkflow'));
    }
  };

  useEffect(() => {
    if (stepperData.CommonWorkflowSteps?.find((row) => row.label != 'Add Step')) {
      if (isEditMode) {
        setStepperData({
          ...allSteps,
          CommonWorkflowSteps: [
            ...(allSteps?.CommonWorkflowSteps || []),
            ...steps.map((step) => ({
              ...step,
              activepieces_automation_id: '',
            })),
          ],
        });
      } else {
        setStepperData({
          ...allSteps,
          CommonWorkflowSteps: [...(allSteps?.CommonWorkflowSteps || [])],
        });
      }
    }
  }, [isEditMode, allSteps]);

  useEffect(() => {
    dispatch(setDsrSelectedWorkflowStep(0));
  }, []);

  const isActualLastStep = activeStep === getActualStepsLength() - 1;

  return (
    <div className="mt-2 flex w-full flex-row gap-5">
      {stepperData?.CommonWorkflowSteps ? (
        <CustomStepper
          variant="circle-alt"
          orientation="vertical"
          initialStep={0}
          steps={stepperData?.CommonWorkflowSteps}
          maxHeight={16.25}
          onClickStep={(value) => handleAddStep(value)}
        >
          {isEditMode ? (
            !stepperData?.CommonWorkflowSteps ? (
              <>No Records..</>
            ) : (
              stepperData?.CommonWorkflowSteps?.map((stepProperties, index) => {
                return index === stepperData?.CommonWorkflowSteps?.length - 1 ? (
                  <Step key={stepProperties.label} icon={Plus} {...stepProperties}>
                    <></>
                  </Step>
                ) : (
                  <Step key={stepProperties.label} {...stepProperties} workFlow={workFlow}>
                    <></>
                  </Step>
                );
              })
            )
          ) : (
            stepperData?.CommonWorkflowSteps.map((stepProperties) => {
              return (
                <Step key={stepProperties?.label} {...stepProperties}>
                  <></>
                </Step>
              );
            })
          )}

          <div className="flex w-[79%] flex-col gap-5">
            <AddDsrWorkflowTasks
              isEditMode={isEditMode || false}
              dsrAddWorkflowTableData={dsrEditVerifyWorkflowTableData}
              activeStep={activeStep}
            />

            <StepperEditFormActions
              isEditMode={true}
              stepperData={stepperData}
              isActualLastStep={isActualLastStep}
            />
          </div>
        </CustomStepper>
      ) : (
        <>No Records...</>
      )}

      {addWorkFlowStatus && (
        <AddWorkflowModal
          handleSubmit={handleAddNewStep}
          isOpen={addWorkFlowStatus}
          setAddTaskModalOpen={() => setaddWorkFlowStatus(!addWorkFlowStatus)}
        />
      )}
    </div>
  );
}

export const StepperEditFormActions: React.FC<DsrEditWorkflowActionsProperties> = ({
  isEditMode,
  stepperData,
  isActualLastStep,
}) => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const {
    prevStep,
    resetSteps,
    isDisabledStep,
    hasCompletedAllSteps,
    isLastStep,
    nextStep,
    activeStep,
  } = useStepper();
  const navigate = useNavigate();
  const location = useLocation();
  const { workFlow, group_id: initialGroupId } = location?.state || {};
  const name = useSelector((state: RootState) => state.globalWorkflow.workflow.flowtype);
  const id = useSelector((state: RootState) => state.globalWorkflow.workflow.id);
  const isDraftDisabled = workFlow === 'published';

  const [workflowName, setWorkflowName] = useState(name);
  const [isSubmitModalOpen, setIsSubmitModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [workflowStatus, setWorkflowList] = useState('published');
  const [workflowStatusDraft, setWorkflowListDraft] = useState('draft');
  const [selectedEntityId, setSelectedEntityId] = useState<string>(
    initialGroupId ? String(initialGroupId) : '-1'
  );

  const handleSubmit = async () => {
    if (!id) {
      toast.error(t('FrontEndErrorMessage.DSR.WorkflowIdMissing'));
      return;
    }

    setIsLoading(true);
    toast.loading(t('FrontEndErrorMessage.DSR.Processing'));
    try {
      const response = await editWorkflow(id, workflowName, workflowStatus, selectedEntityId);

      if (!response.success) {
        throw new Error(response.message || 'Failed to update workflow');
      }

      toast.dismiss();
      toast.success(t('FrontEndErrorMessage.DSR.WorkflowUpdatedSuccessfully'));
      navigate(GLOBAL_WORKFLOW_TABLE);
    } catch (error) {
      toast.dismiss();
      const apiError = error as ApiError;
      if (apiError.response && apiError.response.status === 405) {
        toast.error(t('FrontEndErrorMessage.DSR.InvalidAPIRoute'));
        console.error('API Error:', apiError.response.data);
      } else {
        toast.error(t('FrontEndErrorMessage.DSR.FailedToUpdateWorkflow'));
      }
      console.error('Error updating workflow:', apiError);
    } finally {
      setIsLoading(false);
    }
  };

  const submitAsDraft = async () => {
    if (!id) {
      toast.error(t('FrontEndErrorMessage.DSR.WorkflowIdMissing'));
      return;
    }

    setIsLoading(true);
    toast.loading(t('FrontEndErrorMessage.DSR.Processing'));
    try {
      const response = await editWorkflow(id, workflowName, workflowStatusDraft, selectedEntityId);

      if (!response.success) {
        throw new Error(response.message || 'Failed to update workflow');
      }

      toast.dismiss();
      toast.success(t('FrontEndErrorMessage.DSR.WorkflowUpdatedSuccessfully'));
      navigate(GLOBAL_WORKFLOW_TABLE);
    } catch (error) {
      toast.dismiss();
      const apiError = error as ApiError;
      if (apiError.response && apiError.response.status === 405) {
        toast.error(t('FrontEndErrorMessage.DSR.InvalidAPIRoute'));
        console.error('API Error:', apiError.response.data);
      } else {
        toast.error(t('FrontEndErrorMessage.DSR.FailedToUpdateWorkflow'));
      }
      console.error('Error updating workflow:', apiError);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      {isSubmitModalOpen && (
        <Modal
          open={isSubmitModalOpen}
          onClose={() => setIsSubmitModalOpen(false)}
          cssClass="w-[90%] sm:w-[75%] md:w-[60%] lg:w-[45%] xl:w-[35%] 2xl:w-[30%] max-w-[1300px] shrink-0 rounded-2xl bg-white shadow-[10px_20px_30px_0px_rgba(0,0,0,0.10)]"
        >
          <ConfirmModal
            type="update"
            setIsOpen={setIsSubmitModalOpen}
            handleSubmit={handleSubmit}
            submitAsDraft={submitAsDraft}
          />
        </Modal>
      )}
      <div className="ml-0 mt-2.5 flex h-[40px] w-full items-center justify-end gap-2">
        {hasCompletedAllSteps && isEditMode ? (
          <Button
            className="bg-custom-primary text-white hover:bg-custom-primary"
            size="sm"
            type="button"
            onClick={() => {
              resetSteps();
              dispatch(setDsrSelectedWorkflowStep(0));
            }}
          >
            Reset
          </Button>
        ) : (
          <>
            <Button
              disabled={isDisabledStep}
              onClick={() => {
                prevStep();
                dispatch(setDsrSelectedWorkflowStep(activeStep - 1));
              }}
              size="sm"
              variant="secondary"
              type="button"
            >
              {t('Common.Prev')}
            </Button>
            <Button
              size="sm"
              type="submit"
              className="bg-custom-primary text-white hover:bg-custom-primary hover:text-white"
              onClick={() => {
                if (isActualLastStep && isEditMode) {
                  setIsSubmitModalOpen(true);
                } else {
                  nextStep();
                  dispatch(setDsrSelectedWorkflowStep(activeStep + 1));
                }
              }}
            >
              {isActualLastStep && isEditMode ? t('Common.Save') : t('Common.Next')}
            </Button>
          </>
        )}
      </div>
    </>
  );
};
