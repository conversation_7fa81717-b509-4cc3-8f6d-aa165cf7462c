import React, { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { Button } from '../../../../@/components/ui/Common/Elements/Button/Button';
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../../../../@/components/ui/Common/Elements/Dialog/Dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '../../../../@/components/ui/Common/Elements/Form/Form';
import { Input } from '../../../../@/components/ui/Common/Elements/Input/Input';
import { MultiSelect } from '../../../../@/components/ui/multi-select';
import { Textarea } from '../../../../@/components/ui/textarea';
import httpClient from '../../../../api/httpClientNew';
import addSign from '../../../../assets/plusSign.svg';
import { RootState } from '../../../../redux/store';
import {
  AddTaskModalFormData,
  AddTaskModalProperties,
} from '../../../../types/data-subject-rights';
import { FETCH_ENTITIES } from '../../../common/api';
import { fetchAssignees } from '../../../common/services/data-subject-request';

const AddTaskModal: React.FC<AddTaskModalProperties> = ({
  handleSubmit,
  addTaskModalOpen,
  setAddTaskModalOpen,
  isEditMode,
  isAutomationEnabled,
}) => {
  const group_id = useSelector((state: RootState) => state.globalWorkflow.workflow.group_id);
  const customer_id = useSelector(
    (state: RootState) => state?.auth?.login?.login_details?.customer_id
  );
  const [selectAssignee, setSelectAssignee] = useState<string[]>([]);
  const [userData, setUserData] = useState([]);
  const [entities, setEntities] = useState<any[]>([]);
  const { t } = useTranslation();

  const form = useForm<AddTaskModalFormData>({
    defaultValues: {
      title: '',
      guidance_text: '',
      assignee_id: [],
    },
  });

  const options = userData.map((user: any) => ({
    label: `${user.firstName} ${user.lastName}`,
    value: user.id.toString(),
  }));

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch entities as fallback for group_id
  useEffect(() => {
    const fetchEntities = async () => {
      if (!group_id && customer_id) {
        try {
          console.log('Fetching entities for customer_id:', customer_id);
          const response = await httpClient.get(`${FETCH_ENTITIES}${customer_id}`);
          if (response?.data?.result?.rows?.length > 0) {
            setEntities(response.data.result.rows);
            console.log('Entities fetched:', response.data.result.rows);
          }
        } catch (err) {
          console.error('Error fetching entities:', err);
        }
      }
    };
    fetchEntities();
  }, [customer_id, group_id]);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        // Use group_id from Redux state, or fallback to first entity id
        const effectiveGroupId = group_id || (entities.length > 0 ? entities[0].id : null);

        if (effectiveGroupId) {
          console.log('Fetching assignees for effective group_id:', effectiveGroupId);
          const response = await fetchAssignees(effectiveGroupId);
          console.log('Assignees response:', response);
          setUserData(response || []);
        } else {
          console.log('No effective group_id available');
          setUserData([]);
        }
      } catch (err) {
        console.error('Error fetching assignees:', err);
        setUserData([]);
      }
    };
    fetchUserData();
  }, [group_id, entities]);

  const onSubmit = async (data: AddTaskModalFormData) => {
    setIsSubmitting(true);
    try {
      await handleSubmit(data);
      form.reset();
      setSelectAssignee([]);
    } catch (error) {
      console.error('Form submission failed', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={addTaskModalOpen} onOpenChange={setAddTaskModalOpen}>
      <DialogTrigger asChild>
        {isEditMode && (
          <Button
            style={{
              pointerEvents: 'all',
            }}
            disabled={isAutomationEnabled}
            className={`bg-custom-primary text-white hover:bg-custom-primary hover:text-white ${
              isAutomationEnabled ? `cursor-not-allowed` : ''
            }`}
            onClick={setAddTaskModalOpen}
          >
            <img src={addSign} alt="plus sign" className="size-4" />
            {t('DSR.WorkFlow.AddTask')}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-h-[90%] overflow-auto overflow-y-auto sm:max-w-[50%]">
        <DialogHeader>
          <DialogTitle>{t('DSR.WorkFlow.AddTask')}</DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="col-span-2 gap-4">
              <div className="mt-2 flex gap-4">
                <div className="w-1/2">
                  <FormField
                    control={form.control}
                    name="title"
                    rules={{ required: 'This field is required' }}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('DSR.WorkFlow.TaskTitle')}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input placeholder={t('DSR.TaskOverView.EnterTaskTitle')} {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="w-1/2">
                  <FormField
                    control={form.control}
                    name="due_days"
                    rules={{
                      required: 'This field is required and max due days is 30',
                      min: { value: 1, message: 'Minimum value is 1' },
                      max: { value: 30, message: 'Maximum value is 30' },
                    }}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          {t('DSR.TaskOverView.DueDays')}
                          <span className="text-destructive">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="Enter days"
                            value={field.value ?? ''}
                            onChange={(e) => {
                              let value = e.target.value === '' ? '' : Number(e.target.value);
                              if (typeof value === 'number') {
                                if (value > 30) value = 30;
                                if (value < 1) value = 1;
                              }
                              field.onChange(value);
                            }}
                            onBlur={() => {
                              if (field.value === null) field.onChange('');
                            }}
                            min={1}
                            max={30}
                            className="appearance-none [&::-webkit-inner-spin-button]:hidden [&::-webkit-outer-spin-button]:hidden"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="mt-2">
                <FormField
                  control={form.control}
                  name="assignee_id"
                  render={({ field }) => {
                    console.log(
                      'MultiSelect render - options:',
                      options,
                      'userData:',
                      userData,
                      'field.value:',
                      field.value
                    );
                    return (
                      <FormItem className="w-full">
                        <FormLabel>{t('DSR.WorkFlow.AddAssignee')} </FormLabel>
                        <FormControl>
                          <MultiSelect
                            options={options}
                            value={field.value || []}
                            defaultValue={selectAssignee}
                            placeholder={
                              options.length > 0 ? 'Select Assignee' : 'No assignees available'
                            }
                            onValueChange={(values: string[]) => {
                              console.log('MultiSelect onValueChange:', values);
                              setSelectAssignee(values);
                              field.onChange(values);
                            }}
                            className="h-12 w-full rounded-md border border-solid border-[#CACACA]"
                            variant="secondary"
                            maxCount={5}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              </div>

              <div className="mt-2">
                <FormField
                  control={form.control}
                  name="guidance_text"
                  rules={{
                    required: 'This field is required',
                    maxLength: {
                      value: 500,
                      message: 'Maximum 500 characters allowed',
                    },
                  }}
                  render={({ field }) => (
                    <FormItem className="col-span-2 mt-4">
                      <FormLabel>
                        {t('DSR.WorkFlow.GuidanceText')}
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Textarea
                            placeholder="Enter Note"
                            {...field}
                            onChange={(e) => {
                              if (e.target.value.length <= 500) {
                                field.onChange(e);
                              }
                            }}
                          />
                          <div className="absolute bottom-2 right-2 text-sm text-muted-foreground">
                            {field.value?.length || 0}/500
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <DialogFooter className="flex justify-end">
              <Button type="button" variant="secondary" onClick={setAddTaskModalOpen}>
                {t('Common.Cancel')}
              </Button>
              <Button
                type="submit"
                className="bg-custom-primary text-white hover:bg-custom-primary hover:text-white"
                disabled={isSubmitting}
              >
                {isSubmitting ? t('Common.Adding') : t('DSR.WorkFlow.AddTask')}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddTaskModal;
