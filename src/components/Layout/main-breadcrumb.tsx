import React, { useEffect, useState } from 'react';
import { NavLink, useLocation, useParams } from 'react-router-dom';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbSeparator,
} from '../../@/components/ui/breadcrumb';

import styles from './main-breadcrumb.module.css';

import { useTranslation } from 'react-i18next';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux/store';
import {
  ABOUT_GOTRUST,
  ACTIVITY_AUDIT_LOG,
  ACTIVITY_LOG,
  ADD_CONTROL_CATEGORY,
  ASSESSMENT_ACTION,
  ASSESSMENT_ADD_QUESTION,
  ASSESSMENT_DASHBOARD,
  ASSESSMENT_MANAGEMENT_TASK_OVERVIEW,
  ASSESSMENT_MANAGEMENT_TASK_OVERVIEW_AUDIT_LOG,
  ASSESSMENT_REVIEW,
  ASSESSMENT_VIEW,
  ASSESSMENT_VIEW_AUDIT_LOG,
  ASSESSMENT__TEMPLATES,
  ASSESSMENT__TEMPLATES_VIEW,
  BLOGS,
  BREACH_DETAILS,
  BREACH_LIST,
  BREACH_MANAGEMENT_DASHBOARD,
  CHANGE_PASSWORD,
  // COMMUNITY,
  COMPANY_STRUCTURE,
  COOKIE_CONFIGURATION,
  COOKIE_CONSENT_DOMAIN,
  COOKIE_CONSENT_MANAGEMENT,
  COOKIE_DICTIONARY,
  COOKIE_POLICY,
  COOKIE_POLICY_CREATE,
  COOKIE_POLICY_DETAILS,
  CREATE_TICKET,
  CUSOMER_MANAGEMENT,
  CUSTOMER_MANAGEMENT_ADD_CUSTOMER,
  CUSTOMER_MANAGEMENT_EDIT_CUSTOMER,
  CUSTOMER_MANAGEMENT_VIEW_CUSTOMER,
  CUSTOMIZE,
  DATA_CATALOGUE_DASHBOARD,
  DATA_CATALOGUE_UNSTRUCTURED_V0,
  DATA_CATALOGUE_V0,
  DATA_INSIGHTS,
  DATA_RETENTION_DASHBOARD,
  DATA_SUBJECT_RIGHTS,
  DSR_ADD_QUESTION,
  DSR_ADD_WORKFLOW,
  DSR_ASSIGNEE_VIEW_DETAILS,
  DSR_EDIT_WORKFLOW,
  DSR_EMAIL_TEMPLATES,
  DSR_FORM_BUILDER,
  DSR_FORM_BUILDER_CREATE_FORM,
  DSR_FORM_BUILDER_REVIEW,
  DSR_FORM_BUILDER_VIEW,
  DSR_FORM_REPOSIOTRY,
  DSR_FORM_TRANSLATION,
  DSR_MY_REQUEST,
  DSR_MY_REQUEST_DETAILS,
  DSR_MY_TASK,
  DSR_MY_TASK_VIEW,
  DSR_REQUEST_FORM,
  DSR_RETENTION_SCHEDULE,
  DSR_TASK_OVERVIEW,
  DSR_TASK_OVERVIEW_APPROVED,
  DSR_TASK_OVERVIEW_ARCHIVED,
  DSR_TASK_OVERVIEW_COMPLETED,
  DSR_TASK_OVERVIEW_CREATE,
  DSR_TASK_OVERVIEW_REJECTED,
  DSR_TASK_OVERVIEW_REJECT_IN_PROGRESS,
  DSR_TASK_OVERVIEW_VIEW_APPROVED_REQ,
  DSR_TASK_OVERVIEW_VIEW_ARCHIVED_REQ,
  DSR_TASK_OVERVIEW_VIEW_ASSIGNEE,
  DSR_TASK_OVERVIEW_VIEW_COMPLETED_REQ,
  DSR_TASK_OVERVIEW_VIEW_PENDING_REQ,
  DSR_TASK_OVERVIEW_VIEW_REJECTED_IN_PROGRESS,
  DSR_TASK_OVERVIEW_VIEW_REJECTED_REQ,
  DSR_VIEW_WORKFLOW,
  DSR_WORKFLOW_TABLE,
  EDIT_TICKET_DETAILS,
  FILE_CLASSIFICATION,
  FLOW_DIAGRAM,
  GLOBAL_ADD_WORKFLOW,
  GLOBAL_EDIT_WORKFLOW,
  GLOBAL_VIEW_WORKFLOW,
  GLOBAL_WORKFLOW_TABLE,
  // DOCUMENTS,
  HOME,
  INVOICE,
  PII_ANALYSIS,
  PII_HANDBOOK,
  PII_LIST,
  PII_LIST_TABLE,
  POLICY_MANAGEMENT_AUDIT_LOG,
  POLICY_MANAGEMENT_COOKIE_POLICY,
  // ONBOARDING_QUESTIONS,
  POLICY_MANAGEMENT_DASHBOARD,
  POLICY_MANAGEMENT_RETENTIION_POLICY,
  POLICY_MANAGEMENT_TASK_OVERVIEW,
  POLICY_PRIVACY_NOTICE,
  POLICY_VIEW_PRIVACY_NOTICE,
  PRIVACY_OPS_ACTIVITIES_ACTIONS,
  PRIVACY_OPS_ACTIVITIES_DUTIES,
  PRIVACY_OPS_ACTIVITIES_IMPROVEMENTS,
  PRIVACY_OPS_ASSESSMENT_REPO,
  PRIVACY_OPS_ASSESSMENT_REPO_DETAILS,
  PRIVACY_OPS_COMPLIANCE_DASHBOARD,
  PRIVACY_OPS_CONTROL_DETAILS,
  PRIVACY_OPS_CONTROL_HANDBOOK,
  PRIVACY_OPS_DOCUMENT_REPO,
  PRIVACY_OPS_PROCESSING_ACTIVITIES,
  PRIVACY_OPS_PROCESSING_ACTIVITIES_DETAILS,
  PRIVACY_OPS_REGULATIONS,
  PRIVACY_OPS_REGULATIONS_DETAILS,
  PRIVACY_OPS_RISK_DASHBOARD,
  PRIVACY_OPS_RISK_REGISTER,
  PRIVACY_OPS_RISK_REGISTER_AUDIT,
  PRIVACY_OPS_RISK_REGISTER_DETAILS,
  PRIVACY_POLICY_CREATE_POLICY,
  PRIVACY_POLICY_CREATE_POLICY_TEMPLATE,
  PROFILE,
  RECORDED_CONSENTS,
  RETENTION_RULE_DETAILS,
  RETENTION_RULE_DETAILS_VIEW,
  RETENTION_RULE_LIST,
  ROLE_MANAGEMENT,
  ROLE_MANAGEMENT_ADD_ROLE,
  ROLE_MANAGEMENT_EDIT_ROLE,
  ROLE_MANAGEMENT_EDIT_ROLE_DETAILS,
  ROLE_MANAGEMENT_ROLE_DETAILS,
  ROPA_ADD_QUESTION,
  ROPA_BASIC_INFORMATION,
  ROPA_DASHBOARD,
  ROPA_REGISTER,
  ROPA_RIVIEW,
  ROPA_VIEW,
  ROPA_VIEW_AUDIT_LOG,
  SELECTED_VIEW_GROUP_DETAILS,
  STRUCTURED_DATA_CATALOGUE,
  STRUCTURED_INGESTION,
  STRUCTURED_SERVICES,
  SUPPORT,
  UCF,
  UCF_IMPROVENENT,
  UCM_ADD_COLLECTION_TEMPLATE,
  UCM_CONSENT_PURPOSE,
  UCM_FORM,
  UCM_FORM_CREATE,
  UCM_PII,
  UCM_PRIVACY_NOTICE,
  UCM_PROCESSING_CATEGORY,
  UCM_PROCESSING_PURPOSE,
  UCM_SOURCE_CONSENT_UPLOAD,
  UCM_SUBJECT_CONSENT_LIST,
  UCM_SUBJECT_CONSENT_MANAGER,
  UCM_SUBJECT_CONSENT_MANAGER_LIST,
  UCM_SUBJECT_CONSENT_MANAGER_TEMPLATE,
  UCM_SUBJECT_CONSENT_TYPES,
  UCM_SUBJECT_CONSENT_TYPES_DETAILS,
  UCM_TEMPLATES,
  UNIVERSAL_CONSENT_ADD_PREFERENCECENTRE_CENTER,
  UNIVERSAL_CONSENT_CUSTOME_PARAMETERS,
  UNIVERSAL_CONSENT_MANAGEMENT_CONSENTUPLOADS,
  UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
  UNIVERSAL_CONSENT_MANAGEMENT_FORMCENTRE,
  UNIVERSAL_CONSENT_MANAGEMENT_PREFERENCECENTRE,
  UNIVERSAL_CONSENT_UPDATE_PREFERENCECENTRE_CENTER,
  UNSTRUCTURED_DATA_CATALOGUE,
  UNSTRUCTURED_DATA_MAPPING,
  UNSTRUCTURED_INGESTION,
  UNSTRUCTURED_SERVICES,
  USER_MANAGEMENT,
  VENDOR_RISK_MANAGEMENT_DASHBOARD,
  VIEW_COOKIE_POLICY,
  VIEW_DETAIL_BLOGS,
  VIEW_INTERNAL_ASSESSMENT_QUESTIONS,
  VIEW_TICKET_DETAILS,
  VISUALIZATION,
  VRM_ASSESSMENT_ACTION,
  VRM_ASSESSMENT_ADD_QUESTION,
  VRM_ASSESSMENT_MITIGATION,
  VRM_ASSESSMENT_REVIEW,
  VRM_ASSESSMENT_TEMPLATES,
  // VIDEOS,
  VRM_TASK_OVERVIEW,
  VRM_TASK_OVERVIEW_AUDIT_LOG,
  VRM_TASK_OVERVIEW_CREATE,
  VRM_TEMPLATES_VIEW,
  VRM_VENDOR_DETAILS,
  VRM_VIEW_INTERNAL_ASSESSMENT,
  VRM_VIEW_INTERNAL_ASSESSMENT_AUDIT_LOG,
  VRM_VIEW_VENDOR_ASSESSMENT,
  VRM_VIEW_VENDOR_ASSESSMENT_AUDIT_LOG,
} from '../../utils/routeConstant';

interface BreadcrumbProperties {
  component: string;
  path: string;
}

const MainBreadcrumb = () => {
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbProperties[]>([]);
  const { id } = useParams();
  const location = useLocation();
  const currentAssessmentName = useSelector(
    (state: RootState) => state?.riskAssessment?.selectedRowData?.Assessment?.assessment_name
  );

  const selectedRowID = useSelector(
    (state: RootState) => state?.privacyOps?.selectedRowId
  )?.toString();

  const selectedRegulation = useSelector(
    (state: RootState) => state?.privacyOps?.selectedRegulation
  );
  const createPolicyId = useSelector((state: RootState) => state.policyManagement.createdPolicyId);
  const loginData = useSelector((state: any) => state.auth.login.login_details);

  const privacyPolicyArray = window.location.pathname.split('/');
  const privacyPolicyId = privacyPolicyArray[privacyPolicyArray.length - 1];

  const currentCustomerType = loginData?.customer_type;
  const { t } = useTranslation();

  console.log('ID: ', privacyPolicyId);

  useEffect(() => {
    const currentPath = window.location.pathname;
    const searchParams = new URLSearchParams(window.location.search);
    const isRetentionContext = searchParams.get('context') === 'retention';

    if (currentPath.startsWith(PRIVACY_POLICY_CREATE_POLICY.slice(0, -1))) {
      const currentBreadcrumbs = [
        {
          component: 'Policy Management',
          path: POLICY_MANAGEMENT_DASHBOARD,
        },
        {
          component: isRetentionContext ? 'Retention Policy' : 'All Policies',
          path: isRetentionContext
            ? POLICY_MANAGEMENT_RETENTIION_POLICY
            : POLICY_MANAGEMENT_TASK_OVERVIEW,
        },

        {
          component: 'View Policies',
          path: PRIVACY_POLICY_CREATE_POLICY,
        },
      ];
      if (currentPath.endsWith('/audit-log')) {
        currentBreadcrumbs.push({
          component: 'Audit Log',
          path: `${PRIVACY_POLICY_CREATE_POLICY.slice(0, -1)}/audit-log`,
        });
      }
      setBreadcrumbs(currentBreadcrumbs);
    } else if (currentPath.startsWith(DSR_TASK_OVERVIEW_VIEW_PENDING_REQ)) {
      const queryParameter = window.location.search;

      const currentBreadcrumbs = [
        {
          component: 'Data Subject Rights Management',
          path: DATA_SUBJECT_RIGHTS,
        },
        {
          component: 'Pending Requests',
          path: DSR_TASK_OVERVIEW,
        },
        {
          component: 'View Request',
          path: DSR_TASK_OVERVIEW_VIEW_PENDING_REQ + queryParameter,
        },
      ];
      setBreadcrumbs(currentBreadcrumbs);
    } else if (currentPath.startsWith(DSR_TASK_OVERVIEW_VIEW_APPROVED_REQ)) {
      const queryParameter = window.location.search;

      const currentBreadcrumbs = [
        {
          component: 'Data Subject Rights Management',
          path: DATA_SUBJECT_RIGHTS,
        },
        {
          component: 'Approved Requests',
          path: DSR_TASK_OVERVIEW_APPROVED,
        },
        {
          component: 'View Request',
          path: DSR_TASK_OVERVIEW_VIEW_APPROVED_REQ + queryParameter,
        },
      ];
      setBreadcrumbs(currentBreadcrumbs);
    } else if (currentPath.startsWith(DSR_TASK_OVERVIEW_VIEW_REJECTED_IN_PROGRESS)) {
      const queryParameter = window.location.search;

      const currentBreadcrumbs = [
        {
          component: 'Data Subject Rights Management',
          path: DATA_SUBJECT_RIGHTS,
        },
        {
          component: 'Reject in Progress Requests',
          path: DSR_TASK_OVERVIEW_REJECT_IN_PROGRESS,
        },
        {
          component: 'View Request',
          path: DSR_TASK_OVERVIEW_VIEW_REJECTED_REQ + queryParameter,
        },
      ];
      setBreadcrumbs(currentBreadcrumbs);
    } else if (currentPath.startsWith(DSR_TASK_OVERVIEW_VIEW_REJECTED_REQ)) {
      const queryParameter = window.location.search;

      const currentBreadcrumbs = [
        {
          component: 'Data Subject Rights Management',
          path: DATA_SUBJECT_RIGHTS,
        },
        {
          component: 'Rejected Requests',
          path: DSR_TASK_OVERVIEW_REJECTED,
        },
        {
          component: 'View Request',
          path: DSR_TASK_OVERVIEW_VIEW_REJECTED_REQ + queryParameter,
        },
      ];
      setBreadcrumbs(currentBreadcrumbs);
    } else if (currentPath.startsWith(DSR_TASK_OVERVIEW_VIEW_COMPLETED_REQ)) {
      const queryParameter = window.location.search;

      const currentBreadcrumbs = [
        {
          component: 'Data Subject Rights Management',
          path: DATA_SUBJECT_RIGHTS,
        },
        {
          component: 'Completed Requests',
          path: DSR_TASK_OVERVIEW_COMPLETED,
        },
        {
          component: 'View Request',
          path: DSR_TASK_OVERVIEW_VIEW_COMPLETED_REQ + queryParameter,
        },
      ];
      setBreadcrumbs(currentBreadcrumbs);
    } else if (currentPath.startsWith(DSR_TASK_OVERVIEW_VIEW_ARCHIVED_REQ)) {
      const queryParameter = window.location.search;

      const currentBreadcrumbs = [
        {
          component: 'Data Subject Rights Management',
          path: DATA_SUBJECT_RIGHTS,
        },
        {
          component: 'Archived Requests',
          path: DSR_TASK_OVERVIEW_ARCHIVED,
        },
        {
          component: 'View Request',
          path: DSR_TASK_OVERVIEW_VIEW_ARCHIVED_REQ + queryParameter,
        },
      ];
      setBreadcrumbs(currentBreadcrumbs);
    } else if (currentPath.startsWith(DSR_ASSIGNEE_VIEW_DETAILS)) {
      const queryParameter = window.location.search;

      const currentBreadcrumbs = [
        {
          component: 'Data Subject Rights Management',
          path: DSR_TASK_OVERVIEW,
        },
        {
          component: 'View Request',
          path: DSR_ASSIGNEE_VIEW_DETAILS + queryParameter,
        },
      ];
      setBreadcrumbs(currentBreadcrumbs);
    } else if (currentPath.startsWith(UCM_SUBJECT_CONSENT_MANAGER_TEMPLATE)) {
      const currentBreadcrumbs = [
        {
          component: 'Subject Consent Manager',
          path: UCM_SUBJECT_CONSENT_MANAGER,
        },
      ];
      setBreadcrumbs(currentBreadcrumbs);
    } else if (currentPath.startsWith(UCM_SUBJECT_CONSENT_MANAGER_LIST)) {
      const currentBreadcrumbs = [
        {
          component: 'Subject Consent Manager',
          path: UCM_SUBJECT_CONSENT_MANAGER,
        },
      ];
      setBreadcrumbs(currentBreadcrumbs);
    } else {
      switch (currentPath) {
        case HOME: {
          setBreadcrumbs([]);
          break;
        }
        // ! Profile
        case PROFILE: {
          setBreadcrumbs([
            {
              component: 'Profile',
              path: PROFILE,
            },
          ]);
          break;
        }
        case CHANGE_PASSWORD: {
          setBreadcrumbs([
            {
              component: 'Profile',
              path: PROFILE,
            },
            {
              component: 'Change Password',
              path: CHANGE_PASSWORD,
            },
          ]);
          break;
        }
        // ! Common
        case BLOGS: {
          setBreadcrumbs([
            {
              component: 'Blogs',
              path: BLOGS,
            },
          ]);
          break;
        }
        case VIEW_DETAIL_BLOGS: {
          setBreadcrumbs([
            {
              component: 'Blogs',
              path: BLOGS,
            },
            {
              component: 'View Blog',
              path: VIEW_DETAIL_BLOGS,
            },
          ]);
          break;
        }
        case ABOUT_GOTRUST: {
          setBreadcrumbs([
            {
              component: 'About GoTrust',
              path: ABOUT_GOTRUST,
            },
          ]);
          break;
        }
        // ! Company Structure
        case COMPANY_STRUCTURE: {
          setBreadcrumbs([
            {
              component: 'Company Structure',
              path: COMPANY_STRUCTURE,
            },
          ]);
          break;
        }
        case SELECTED_VIEW_GROUP_DETAILS: {
          setBreadcrumbs([
            {
              component: 'Company Structure',
              path: COMPANY_STRUCTURE,
            },
            {
              component: 'Group Detail',
              path: SELECTED_VIEW_GROUP_DETAILS,
            },
          ]);
          break;
        }
        // ! Role Management
        case ROLE_MANAGEMENT: {
          setBreadcrumbs([
            {
              component: 'Role Management',
              path: ROLE_MANAGEMENT,
            },
          ]);
          break;
        }
        case ROLE_MANAGEMENT_ROLE_DETAILS: {
          setBreadcrumbs([
            {
              component: 'Role Management',
              path: ROLE_MANAGEMENT_ROLE_DETAILS,
            },
          ]);
          break;
        }
        case ROLE_MANAGEMENT_ADD_ROLE: {
          setBreadcrumbs([
            {
              component: 'Role Management',
              path: ROLE_MANAGEMENT_ADD_ROLE,
            },
          ]);
          break;
        }
        case ROLE_MANAGEMENT_EDIT_ROLE: {
          setBreadcrumbs([
            {
              component: 'Role Management',
              path: ROLE_MANAGEMENT_EDIT_ROLE,
            },
          ]);
          break;
        }
        case ROLE_MANAGEMENT_EDIT_ROLE_DETAILS: {
          setBreadcrumbs([
            {
              component: 'Role Management',
              path: ROLE_MANAGEMENT_EDIT_ROLE_DETAILS,
            },
          ]);
          break;
        }
        // ! User Management
        case USER_MANAGEMENT: {
          setBreadcrumbs([
            {
              component: 'User Management',
              path: USER_MANAGEMENT,
            },
          ]);
          break;
        }
        // ! Ropa
        case ROPA_DASHBOARD: {
          setBreadcrumbs([
            {
              component: 'Insights into Processing Activities',
              path: ROPA_DASHBOARD,
            },
          ]);
          break;
        }
        case ACTIVITY_LOG: {
          setBreadcrumbs([
            {
              component: 'Task Overview',
              path: ACTIVITY_LOG,
            },
          ]);
          break;
        }
        case ACTIVITY_AUDIT_LOG: {
          setBreadcrumbs([
            {
              component: 'Task Overview',
              path: ACTIVITY_LOG,
            },
            {
              component: 'Audit Log',
              path: ACTIVITY_AUDIT_LOG,
            },
          ]);
          break;
        }
        case ROPA_VIEW: {
          setBreadcrumbs([
            {
              component: 'Task Overview',
              path: ACTIVITY_LOG,
            },
            {
              component: 'ROPA',
              path: ROPA_VIEW,
            },
          ]);
          break;
        }
        case ROPA_VIEW_AUDIT_LOG: {
          setBreadcrumbs([
            {
              component: 'Task Overview',
              path: ACTIVITY_LOG,
            },
            {
              component: 'ROPA',
              path: ROPA_VIEW,
            },
            {
              component: 'Audit Log',
              path: ROPA_VIEW_AUDIT_LOG,
            },
          ]);
          break;
        }
        case ROPA_BASIC_INFORMATION: {
          setBreadcrumbs([
            {
              component: 'Task Overview',
              path: ACTIVITY_LOG,
            },
            {
              component: 'Basic Information',
              path: ROPA_BASIC_INFORMATION,
            },
          ]);
          break;
        }
        case ROPA_RIVIEW: {
          setBreadcrumbs([
            {
              component: 'Task Overview',
              path: ACTIVITY_LOG,
            },
            {
              component: 'ROPA Review',
              path: ROPA_RIVIEW,
            },
          ]);
          break;
        }
        case ROPA_REGISTER.replace(':id', `${id}`): {
          // Check if we're coming from Privacy Ops
          const source = (location.state as any)?.source;

          if (source === 'privacy-ops') {
            setBreadcrumbs([
              {
                component: 'Privacy Ops',
                path: '/home',
              },
              {
                component: 'Processing Activities',
                path: PRIVACY_OPS_PROCESSING_ACTIVITIES,
              },
              {
                component: 'View ROPA',
                path: `/data-mapping/ropa/register/${id}`,
              },
            ]);
          } else {
            setBreadcrumbs([
              {
                component: 'Task Overview',
                path: ACTIVITY_LOG,
              },
              {
                component: 'ROPA',
                path: `/data-mapping/ropa/register/${id}`,
              },
            ]);
          }
          break;
        }

        case ROPA_ADD_QUESTION: {
          setBreadcrumbs([
            {
              component: 'Task Overview',
              path: ACTIVITY_LOG,
            },
            {
              component: 'ROPA',
              path: ROPA_REGISTER,
            },
            {
              component: 'Add Control',
              path: ROPA_ADD_QUESTION,
            },
          ]);
          break;
        }
        case UNSTRUCTURED_DATA_MAPPING: {
          setBreadcrumbs([
            {
              component: 'Task Overview',
              path: ACTIVITY_LOG,
            },
            {
              component: 'PII Inventory',
              path: UNSTRUCTURED_DATA_MAPPING,
            },
          ]);
          break;
        }
        case PII_LIST: {
          setBreadcrumbs([
            {
              component: 'Task Overview',
              path: ACTIVITY_LOG,
            },
            {
              component: 'Unstructured Data Inventory',
              path: PII_LIST,
            },
          ]);
          break;
        }
        case STRUCTURED_DATA_CATALOGUE: {
          setBreadcrumbs([
            {
              component: 'Structured',
              path: STRUCTURED_DATA_CATALOGUE,
            },
          ]);
          break;
        }
        case UNSTRUCTURED_DATA_CATALOGUE: {
          setBreadcrumbs([{ component: 'Unstructured', path: UNSTRUCTURED_DATA_CATALOGUE }]);
          break;
        }
        case UNSTRUCTURED_SERVICES: {
          setBreadcrumbs([
            { component: 'Unstructured', path: UNSTRUCTURED_DATA_CATALOGUE },
            {
              component: 'Services',
              path: UNSTRUCTURED_SERVICES,
            },
          ]);
          break;
        }
        case UNSTRUCTURED_INGESTION: {
          setBreadcrumbs([
            { component: 'Unstructured', path: UNSTRUCTURED_DATA_CATALOGUE },
            {
              component: 'Services',
              path: UNSTRUCTURED_SERVICES,
            },
            {
              component: 'Ingestion',
              path: UNSTRUCTURED_INGESTION,
            },
          ]);
          break;
        }
        case STRUCTURED_SERVICES: {
          setBreadcrumbs([
            { component: 'Structured', path: STRUCTURED_DATA_CATALOGUE },
            {
              component: 'Services',
              path: STRUCTURED_SERVICES,
            },
          ]);
          break;
        }
        case STRUCTURED_INGESTION: {
          setBreadcrumbs([
            { component: 'Structured', path: STRUCTURED_DATA_CATALOGUE },
            {
              component: 'Services',
              path: STRUCTURED_SERVICES,
            },
            {
              component: 'Ingestion',
              path: STRUCTURED_INGESTION,
            },
          ]);
          break;
        }
        case DATA_CATALOGUE_V0: {
          setBreadcrumbs([
            {
              component: 'Data Catalogue',
              path: DATA_CATALOGUE_V0,
            },
            { component: 'Structured', path: DATA_CATALOGUE_V0 },
          ]);
          break;
        }
        case DATA_CATALOGUE_UNSTRUCTURED_V0: {
          setBreadcrumbs([
            {
              component: 'Data Catalogue',
              path: DATA_CATALOGUE_V0,
            },
            { component: 'Unstructured', path: DATA_CATALOGUE_UNSTRUCTURED_V0 },
          ]);
          break;
        }
        case DATA_CATALOGUE_DASHBOARD: {
          setBreadcrumbs([
            {
              component: 'Data Catalogue Dashboard',
              path: DATA_CATALOGUE_DASHBOARD,
            },
          ]);
          break;
        }
        case PII_LIST_TABLE: {
          setBreadcrumbs([
            {
              component: 'Data Catalogue Dashboard',
              path: DATA_CATALOGUE_DASHBOARD,
            },
            {
              component: 'PII List',
              path: PII_LIST_TABLE,
            },
          ]);
          break;
        }
        case FLOW_DIAGRAM: {
          setBreadcrumbs([
            {
              component: 'Flow Diagram',
              path: FLOW_DIAGRAM,
            },
          ]);
          break;
        }
        case FILE_CLASSIFICATION: {
          setBreadcrumbs([
            {
              component: 'File Classification',
              path: FILE_CLASSIFICATION,
            },
          ]);
          break;
        }

        case DATA_INSIGHTS: {
          setBreadcrumbs([
            {
              component: 'Data Insights',
              path: DATA_INSIGHTS,
            },
          ]);
          break;
        }
        case PII_HANDBOOK: {
          setBreadcrumbs([
            {
              component: 'Data Insights',
              path: DATA_INSIGHTS,
            },
            {
              component: 'PII Handbook',
              path: PII_HANDBOOK,
            },
          ]);
          break;
        }

        case PII_ANALYSIS: {
          setBreadcrumbs([
            {
              component: 'PII Visualization',
              path: PII_ANALYSIS,
            },
          ]);
          break;
        }

        case DSR_FORM_REPOSIOTRY: {
          setBreadcrumbs([
            {
              component: 'DSR Form Repository',
              path: DSR_FORM_REPOSIOTRY,
            },
          ]);
          break;
        }
        case DSR_FORM_BUILDER: {
          setBreadcrumbs([
            {
              component: 'DSR Form Builder',
              path: DSR_FORM_BUILDER,
            },
          ]);
          break;
        }
        case DSR_FORM_BUILDER_CREATE_FORM: {
          setBreadcrumbs([
            {
              component: 'DSR Form Builder',
              path: DSR_FORM_BUILDER,
            },
            {
              component: 'Create Form',
              path: DSR_FORM_BUILDER_CREATE_FORM,
            },
          ]);
          break;
        }
        case DSR_FORM_BUILDER_VIEW: {
          setBreadcrumbs([
            {
              component: 'DSR Form Builder',
              path: DSR_FORM_BUILDER,
            },
            {
              component: 'View',
              path: DSR_FORM_BUILDER_VIEW,
            },
          ]);
          break;
        }
        case DSR_FORM_BUILDER_REVIEW: {
          setBreadcrumbs([
            {
              component: 'DSR Form Builder',
              path: DSR_FORM_BUILDER,
            },
            {
              component: 'Create Form',
              path: DSR_FORM_BUILDER_CREATE_FORM,
            },
            {
              component: 'Form Review',
              path: DSR_FORM_BUILDER_REVIEW,
            },
          ]);
          break;
        }
        case DSR_ADD_QUESTION: {
          setBreadcrumbs([
            {
              component: 'DSR Form Builder',
              path: DSR_FORM_BUILDER,
            },
            {
              component: 'Create Form',
              path: DSR_FORM_BUILDER_CREATE_FORM,
            },
            {
              component: 'Add Question',
              path: DSR_ADD_QUESTION,
            },
          ]);
          break;
        }
        case DSR_FORM_TRANSLATION: {
          setBreadcrumbs([
            {
              component: 'DSR Form Builder',
              path: DSR_FORM_BUILDER,
            },
            {
              component: 'Form Translation',
              path: DSR_FORM_TRANSLATION,
            },
          ]);
          break;
        }

        // ! Policy Management
        case POLICY_MANAGEMENT_DASHBOARD: {
          setBreadcrumbs([
            {
              component: 'Policy Management',
              path: POLICY_MANAGEMENT_DASHBOARD,
            },
          ]);
          break;
        }
        case POLICY_MANAGEMENT_TASK_OVERVIEW: {
          setBreadcrumbs([
            {
              component: 'Policy Management',
              path: POLICY_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'All Policies',
              path: POLICY_MANAGEMENT_TASK_OVERVIEW,
            },
          ]);
          break;
        }
        case POLICY_MANAGEMENT_AUDIT_LOG: {
          setBreadcrumbs([
            {
              component: 'Policy Management',
              path: POLICY_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Audit Log',
              path: POLICY_MANAGEMENT_AUDIT_LOG,
            },
          ]);
          break;
        }
        case POLICY_PRIVACY_NOTICE: {
          setBreadcrumbs([
            {
              component: 'Policy Management',
              path: POLICY_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Privacy Notice',
              path: POLICY_PRIVACY_NOTICE,
            },
          ]);
          break;
        }
        case POLICY_MANAGEMENT_COOKIE_POLICY: {
          setBreadcrumbs([
            {
              component: 'Policy Management',
              path: POLICY_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Cookie Policy',
              path: POLICY_MANAGEMENT_COOKIE_POLICY,
            },
          ]);
          break;
        }
        case `${POLICY_PRIVACY_NOTICE}/${privacyPolicyId}`: {
          setBreadcrumbs([
            {
              component: 'Policy Management',
              path: POLICY_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Privacy Notice',
              path: POLICY_PRIVACY_NOTICE,
            },
            {
              component: 'Privacy Notice Details',
              path: `${POLICY_PRIVACY_NOTICE}/${privacyPolicyId}`,
            },
          ]);
          break;
        }
        case `${POLICY_VIEW_PRIVACY_NOTICE}/${privacyPolicyId}`: {
          setBreadcrumbs([
            {
              component: 'Policy Management',
              path: POLICY_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Privacy Notice',
              path: POLICY_PRIVACY_NOTICE,
            },
            {
              component: 'View',
              path: `${POLICY_VIEW_PRIVACY_NOTICE}/${privacyPolicyId}`,
            },
          ]);
          break;
        }
        case PRIVACY_POLICY_CREATE_POLICY_TEMPLATE: {
          setBreadcrumbs([
            {
              component: 'Policy Management',
              path: POLICY_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'All Policies',
              path: POLICY_MANAGEMENT_TASK_OVERVIEW,
            },
            {
              component: 'Privacy Notice',
              path: `${PRIVACY_POLICY_CREATE_POLICY.slice(0, -1)}${createPolicyId}`,
            },
            {
              component: 'Template',
              path: `${PRIVACY_POLICY_CREATE_POLICY_TEMPLATE}`,
            },
          ]);
          break;
        }
        // ! Assessment Management
        case ASSESSMENT_DASHBOARD: {
          setBreadcrumbs([
            {
              component: 'Assessment Management',
              path: ASSESSMENT_DASHBOARD,
            },
            // {
            //   component: 'Dashboard',
            //   path: ASSESSMENT_DASHBOARD,
            // },
          ]);
          break;
        }
        case ASSESSMENT_MANAGEMENT_TASK_OVERVIEW: {
          setBreadcrumbs([
            {
              component: 'Assessment Management',
              path: ASSESSMENT_DASHBOARD,
            },
            {
              component: 'Task Overview',
              path: ASSESSMENT_MANAGEMENT_TASK_OVERVIEW,
            },
          ]);
          break;
        }
        case ASSESSMENT_MANAGEMENT_TASK_OVERVIEW_AUDIT_LOG: {
          setBreadcrumbs([
            {
              component: 'Assessment Management',
              path: ASSESSMENT_DASHBOARD,
            },
            {
              component: 'Task Overview',
              path: ASSESSMENT_MANAGEMENT_TASK_OVERVIEW,
            },
            {
              component: 'Audit Log',
              path: ASSESSMENT_MANAGEMENT_TASK_OVERVIEW_AUDIT_LOG,
            },
          ]);
          break;
        }
        case ASSESSMENT_VIEW: {
          setBreadcrumbs([
            {
              component: 'Assessment Management',
              path: ASSESSMENT_DASHBOARD,
            },
            {
              component: 'Task Overview',
              path: ASSESSMENT_MANAGEMENT_TASK_OVERVIEW,
            },
            {
              component: currentAssessmentName,
              path: ASSESSMENT_VIEW,
            },
          ]);
          break;
        }
        case ASSESSMENT_VIEW_AUDIT_LOG: {
          setBreadcrumbs([
            {
              component: 'Assessment Management',
              path: ASSESSMENT_DASHBOARD,
            },
            {
              component: 'Task Overview',
              path: ASSESSMENT_MANAGEMENT_TASK_OVERVIEW,
            },
            {
              component: currentAssessmentName,
              path: ASSESSMENT_VIEW,
            },
            {
              component: 'Audit Log',
              path: ASSESSMENT_VIEW_AUDIT_LOG,
            },
          ]);
          break;
        }
        case ASSESSMENT_ACTION.replace(':id', `${id}`): {
          setBreadcrumbs([
            {
              component: 'Assessment Management',
              path: ASSESSMENT_DASHBOARD,
            },
            {
              component: 'Task Overview',
              path: ASSESSMENT_MANAGEMENT_TASK_OVERVIEW,
            },
            {
              component: currentAssessmentName,
              path: `/assessment-management/task-overview/controls/${id}`,
            },
          ]);
          break;
        }
        case ASSESSMENT_REVIEW: {
          setBreadcrumbs([
            {
              component: 'Assessment Management',
              path: ASSESSMENT_DASHBOARD,
            },
            {
              component: 'Task Overview',
              path: ASSESSMENT_MANAGEMENT_TASK_OVERVIEW,
            },
            {
              component: currentAssessmentName,
              path: ASSESSMENT_REVIEW,
            },
          ]);
          break;
        }
        case ASSESSMENT_ADD_QUESTION: {
          setBreadcrumbs([
            {
              component: 'Assessment Management',
              path: ASSESSMENT_DASHBOARD,
            },
            {
              component: 'Task Overview',
              path: ASSESSMENT_MANAGEMENT_TASK_OVERVIEW,
            },
            {
              component: currentAssessmentName,
              path: ASSESSMENT_ACTION,
            },
            {
              component: 'Add Control',
              path: ASSESSMENT_ADD_QUESTION,
            },
          ]);
          break;
        }
        case ASSESSMENT__TEMPLATES: {
          setBreadcrumbs([
            {
              component: 'Assessment Management',
              path: ASSESSMENT_DASHBOARD,
            },
            {
              component: 'Templates',
              path: ASSESSMENT__TEMPLATES,
            },
          ]);
          break;
        }
        case ASSESSMENT__TEMPLATES_VIEW: {
          setBreadcrumbs([
            {
              component: 'Assessment Management',
              path: ASSESSMENT_DASHBOARD,
            },
            {
              component: 'Templates',
              path: ASSESSMENT__TEMPLATES,
            },
            {
              component: 'Templates View',
              path: ASSESSMENT__TEMPLATES_VIEW,
            },
          ]);
          break;
        }
        // ! Vendor Risk Management
        case VENDOR_RISK_MANAGEMENT_DASHBOARD: {
          setBreadcrumbs([
            {
              component: 'Vendor Risk Management',
              path: VENDOR_RISK_MANAGEMENT_DASHBOARD,
            },
          ]);
          break;
        }
        case VRM_TASK_OVERVIEW: {
          setBreadcrumbs(
            currentCustomerType === 'CUSTOMER'
              ? [
                  {
                    component: 'Vendor Risk Management',
                    path: VENDOR_RISK_MANAGEMENT_DASHBOARD,
                  },
                  {
                    component: 'Vendor List',
                    path: VRM_TASK_OVERVIEW,
                  },
                ]
              : [
                  {
                    component: 'Vendor List',
                    path: VRM_TASK_OVERVIEW,
                  },
                ]
          );
          break;
        }
        case VRM_TASK_OVERVIEW_AUDIT_LOG.replace(':id', `${id}`): {
          setBreadcrumbs([
            {
              component: 'Vendor Risk Management',
              path: VENDOR_RISK_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Vendor List',
              path: VRM_TASK_OVERVIEW,
            },
            {
              component: 'Audit Log',
              path: VRM_TASK_OVERVIEW_AUDIT_LOG.replace(':id', `${id}`),
            },
          ]);
          break;
        }
        case VRM_VENDOR_DETAILS.replace(':id', `${id}`): {
          setBreadcrumbs([
            {
              component: 'Vendor Risk Management',
              path: VENDOR_RISK_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Vendor List',
              path: VRM_TASK_OVERVIEW,
            },
            {
              component: 'Details',
              path: VRM_VENDOR_DETAILS,
            },
          ]);
          break;
        }
        case VRM_TASK_OVERVIEW_CREATE: {
          setBreadcrumbs([
            {
              component: 'Vendor Risk Management',
              path: VENDOR_RISK_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Vendor List',
              path: VRM_TASK_OVERVIEW,
            },
            {
              component: 'Details',
              path: VRM_TASK_OVERVIEW_CREATE,
            },
          ]);
          break;
        }
        case VRM_VIEW_INTERNAL_ASSESSMENT: {
          setBreadcrumbs([
            {
              component: 'Vendor Risk Management',
              path: VENDOR_RISK_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Vendor List',
              path: VRM_TASK_OVERVIEW,
            },
            {
              component: 'Details',
              path: VRM_TASK_OVERVIEW_CREATE,
            },
            {
              component: 'Internal Assessment',
              path: VRM_VIEW_INTERNAL_ASSESSMENT,
            },
          ]);
          break;
        }
        case VIEW_INTERNAL_ASSESSMENT_QUESTIONS: {
          setBreadcrumbs([
            {
              component: 'Vendor Risk Management',
              path: VENDOR_RISK_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Vendor List',
              path: VRM_TASK_OVERVIEW,
            },
            {
              component: 'Details',
              path: VRM_TASK_OVERVIEW_CREATE,
            },
            {
              component: 'View Assesment',
              path: VIEW_INTERNAL_ASSESSMENT_QUESTIONS,
            },
          ]);
          break;
        }
        case VRM_VIEW_INTERNAL_ASSESSMENT_AUDIT_LOG: {
          setBreadcrumbs([
            {
              component: 'Vendor Risk Management',
              path: VENDOR_RISK_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Vendor List',
              path: VRM_TASK_OVERVIEW,
            },
            {
              component: 'Details',
              path: VRM_TASK_OVERVIEW_CREATE,
            },
            {
              component: 'Internal Assessment',
              path: VRM_VIEW_INTERNAL_ASSESSMENT,
            },
            {
              component: 'Audit Log',
              path: VRM_VIEW_INTERNAL_ASSESSMENT_AUDIT_LOG,
            },
          ]);
          break;
        }
        case VRM_VIEW_VENDOR_ASSESSMENT: {
          setBreadcrumbs(
            currentCustomerType === 'CUSTOMER'
              ? [
                  {
                    component: 'Vendor Risk Management',
                    path: VENDOR_RISK_MANAGEMENT_DASHBOARD,
                  },
                  {
                    component: 'Vendor List',
                    path: VRM_TASK_OVERVIEW,
                  },
                  {
                    component: 'Details',
                    path: VRM_TASK_OVERVIEW_CREATE,
                  },
                  {
                    component: 'Vendor Assessment',
                    path: VRM_VIEW_VENDOR_ASSESSMENT,
                  },
                ]
              : [
                  {
                    component: 'Vendor List',
                    path: VRM_TASK_OVERVIEW,
                  },
                  {
                    component: 'Vendor Assessment',
                    path: VRM_VIEW_VENDOR_ASSESSMENT,
                  },
                ]
          );
          // setBreadcrumbs([
          //   {
          //     component: 'Vendor Risk Management',
          //     path: VENDOR_RISK_MANAGEMENT_DASHBOARD,
          //   },
          //   {
          //     component: 'Vendor List',
          //     path: VRM_TASK_OVERVIEW,
          //   },
          //   {
          //     component: 'Details',
          //     path: VRM_TASK_OVERVIEW_CREATE,
          //   },
          //   {
          //     component: 'Vendor Assessment',
          //     path: VRM_VIEW_VENDOR_ASSESSMENT,
          //   },
          // ]);
          break;
        }
        case VRM_VIEW_VENDOR_ASSESSMENT_AUDIT_LOG: {
          setBreadcrumbs(
            currentCustomerType === 'CUSTOMER'
              ? [
                  {
                    component: 'Vendor Risk Management',
                    path: VENDOR_RISK_MANAGEMENT_DASHBOARD,
                  },
                  {
                    component: 'Vendor List',
                    path: VRM_TASK_OVERVIEW,
                  },
                  {
                    component: 'Details',
                    path: VRM_TASK_OVERVIEW_CREATE,
                  },
                  {
                    component: 'Vendor Assessment',
                    path: VRM_VIEW_VENDOR_ASSESSMENT,
                  },
                  {
                    component: 'Audit Log',
                    path: VRM_VIEW_VENDOR_ASSESSMENT_AUDIT_LOG,
                  },
                ]
              : [
                  {
                    component: 'Vendor List',
                    path: VRM_TASK_OVERVIEW,
                  },
                  {
                    component: 'Vendor Assessment',
                    path: VRM_VIEW_VENDOR_ASSESSMENT,
                  },
                  {
                    component: 'Audit Log',
                    path: VRM_VIEW_VENDOR_ASSESSMENT_AUDIT_LOG,
                  },
                ]
          );
          break;
        }
        case VRM_ASSESSMENT_ACTION: {
          setBreadcrumbs(
            currentCustomerType === 'CUSTOMER'
              ? [
                  {
                    component: 'Vendor Risk Management',
                    path: VENDOR_RISK_MANAGEMENT_DASHBOARD,
                  },
                  {
                    component: 'Vendor List',
                    path: VRM_TASK_OVERVIEW,
                  },
                  {
                    component: 'Details',
                    path: VRM_TASK_OVERVIEW_CREATE,
                  },
                  {
                    component: currentAssessmentName,
                    path: VRM_ASSESSMENT_ACTION,
                  },
                ]
              : [
                  {
                    component: 'Vendor List',
                    path: VRM_TASK_OVERVIEW,
                  },
                  {
                    component: currentAssessmentName,
                    path: VRM_ASSESSMENT_ACTION,
                  },
                ]
          );
          break;
        }
        case VRM_ASSESSMENT_ADD_QUESTION: {
          setBreadcrumbs([
            {
              component: 'Vendor Risk Management',
              path: VENDOR_RISK_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Vendor List',
              path: VRM_TASK_OVERVIEW,
            },
            {
              component: 'Details',
              path: VRM_TASK_OVERVIEW_CREATE,
            },
            {
              component: currentAssessmentName,
              path: VRM_ASSESSMENT_ACTION,
            },
            {
              component: 'Add Control',
              path: VRM_ASSESSMENT_ADD_QUESTION,
            },
          ]);
          break;
        }
        case VRM_ASSESSMENT_REVIEW: {
          setBreadcrumbs([
            {
              component: 'Vendor Risk Management',
              path: VENDOR_RISK_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Vendor List',
              path: VRM_TASK_OVERVIEW,
            },
            {
              component: 'Details',
              path: VRM_TASK_OVERVIEW_CREATE,
            },
            {
              component: currentAssessmentName,
              path: VRM_ASSESSMENT_REVIEW,
            },
          ]);
          break;
        }
        case VRM_ASSESSMENT_TEMPLATES: {
          setBreadcrumbs([
            {
              component: 'Vendor Risk Management',
              path: VENDOR_RISK_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Templates',
              path: VRM_ASSESSMENT_TEMPLATES,
            },
          ]);
          break;
        }
        case VRM_TEMPLATES_VIEW: {
          setBreadcrumbs([
            {
              component: 'Vendor Risk Management',
              path: VENDOR_RISK_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Templates',
              path: VRM_ASSESSMENT_TEMPLATES,
            },
            {
              component: 'Templates View',
              path: VRM_TEMPLATES_VIEW,
            },
          ]);
          break;
        }
        case VRM_ASSESSMENT_MITIGATION: {
          setBreadcrumbs([
            {
              component: 'Vendor Risk Management',
              path: VENDOR_RISK_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Vendor List',
              path: VRM_TASK_OVERVIEW,
            },
            {
              component: 'Details',
              path: VRM_TASK_OVERVIEW_CREATE,
            },
            {
              component: 'Mitigation',
              path: VRM_ASSESSMENT_MITIGATION,
            },
          ]);
          break;
        }
        // ! Cookie Consent Management
        case COOKIE_CONSENT_MANAGEMENT: {
          setBreadcrumbs([
            {
              component: 'Cookie Consent Management',
              path: COOKIE_CONSENT_MANAGEMENT,
            },
          ]);
          break;
        }
        case COOKIE_CONSENT_DOMAIN: {
          setBreadcrumbs([
            {
              component: 'Cookie Consent Management',
              path: COOKIE_CONSENT_MANAGEMENT,
            },
            {
              component: 'Cookie Consent Domain',
              path: COOKIE_CONSENT_DOMAIN,
            },
          ]);
          break;
        }
        case COOKIE_CONFIGURATION: {
          setBreadcrumbs([
            {
              component: 'Cookie Consent Management',
              path: COOKIE_CONSENT_MANAGEMENT,
            },
            {
              component: 'Cookie Consent Domain',
              path: COOKIE_CONSENT_DOMAIN,
            },
            {
              component: 'Cookie Configuration',
              path: COOKIE_CONFIGURATION,
            },
          ]);
          break;
        }
        case RECORDED_CONSENTS: {
          setBreadcrumbs([
            {
              component: 'Recorded Consents',
              path: RECORDED_CONSENTS,
            },
          ]);
          break;
        }
        case COOKIE_DICTIONARY: {
          setBreadcrumbs([
            {
              component: 'Cookie Dictionary',
              path: COOKIE_DICTIONARY,
            },
          ]);
          break;
        }
        case COOKIE_POLICY: {
          setBreadcrumbs([
            {
              component: 'Cookie Policy',
              path: COOKIE_POLICY,
            },
          ]);
          break;
        }
        case COOKIE_POLICY_DETAILS.replace(':id', `${id}`): {
          setBreadcrumbs([
            {
              component: 'Cookie Policy',
              path: COOKIE_POLICY,
            },
            {
              component: 'Edit Cookie Policy ',
              path: COOKIE_POLICY_DETAILS,
            },
          ]);
          break;
        }
        case COOKIE_POLICY_CREATE:
          {
            setBreadcrumbs([
              {
                component: 'Cookie Policy',
                path: COOKIE_POLICY,
              },
              {
                component: 'Create Cookie Policy',
                path: COOKIE_POLICY_CREATE,
              },
            ]);
            break;
          }
          COOKIE_POLICY_CREATE;
        case VIEW_COOKIE_POLICY.replace(':id', `${id}`): {
          setBreadcrumbs([
            {
              component: 'Cookie Policy',
              path: COOKIE_POLICY,
            },
            {
              component: 'View Cookie Policy',
              path: VIEW_COOKIE_POLICY,
            },
          ]);
          break;
        }
        // ! Universal Consent Management
        case UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD: {
          setBreadcrumbs([
            {
              component: 'Universal Consent Management',
              path: UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
            },
          ]);
          break;
        }
        case UNIVERSAL_CONSENT_MANAGEMENT_PREFERENCECENTRE: {
          setBreadcrumbs([
            {
              component: 'Universal Consent Management',
              path: UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
            },

            {
              component: 'Preference Center',
              path: UNIVERSAL_CONSENT_MANAGEMENT_PREFERENCECENTRE,
            },
          ]);
          break;
        }
        case UCM_PRIVACY_NOTICE: {
          setBreadcrumbs([
            {
              component: 'Privacy Notice',
              path: UCM_PRIVACY_NOTICE,
            },
          ]);
          break;
        }
        case `${UCM_PRIVACY_NOTICE}/${id}`: {
          setBreadcrumbs([
            {
              component: 'Privacy Notice',
              path: UCM_PRIVACY_NOTICE,
            },
            {
              component: 'Privacy Notice Details',
              path: `${UCM_PRIVACY_NOTICE}/${id}`,
            },
          ]);
          break;
        }
        case `${UCM_PRIVACY_NOTICE}/view/${id}`: {
          setBreadcrumbs([
            {
              component: 'Privacy Notice',
              path: UCM_PRIVACY_NOTICE,
            },
            {
              component: 'View Privacy Notice',
              path: `${UCM_PRIVACY_NOTICE}/view/${id}`,
            },
          ]);
          break;
        }
        case UNIVERSAL_CONSENT_ADD_PREFERENCECENTRE_CENTER: {
          setBreadcrumbs([
            {
              component: 'Universal Consent Management',
              path: UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
            },

            {
              component: 'Preference Center',
              path: UNIVERSAL_CONSENT_MANAGEMENT_PREFERENCECENTRE,
            },
            {
              component: 'Add Preference Center',
              path: UNIVERSAL_CONSENT_ADD_PREFERENCECENTRE_CENTER,
            },
          ]);
          break;
        }
        case UNIVERSAL_CONSENT_UPDATE_PREFERENCECENTRE_CENTER: {
          setBreadcrumbs([
            {
              component: 'Universal Consent Management',
              path: UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
            },

            {
              component: 'Preference Center',
              path: UNIVERSAL_CONSENT_MANAGEMENT_PREFERENCECENTRE,
            },
            {
              component: 'Update Preference Center',
              path: UNIVERSAL_CONSENT_UPDATE_PREFERENCECENTRE_CENTER,
            },
          ]);
          break;
        }
        case UNIVERSAL_CONSENT_MANAGEMENT_FORMCENTRE: {
          setBreadcrumbs([
            {
              component: 'Universal Consent Management',
              path: UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Consent Collection',
              path: UNIVERSAL_CONSENT_MANAGEMENT_FORMCENTRE,
            },
          ]);
          break;
        }
        case UNIVERSAL_CONSENT_MANAGEMENT_CONSENTUPLOADS: {
          setBreadcrumbs([
            {
              component: 'Universal Consent Management',
              path: UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Consent Upload',
              path: UNIVERSAL_CONSENT_MANAGEMENT_CONSENTUPLOADS,
            },
          ]);
          break;
        }
        case UNIVERSAL_CONSENT_CUSTOME_PARAMETERS: {
          setBreadcrumbs([
            {
              component: 'Universal Consent Management',
              path: UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Custom Parameters',
              path: UNIVERSAL_CONSENT_CUSTOME_PARAMETERS,
            },
          ]);
          break;
        }
        // case UNIVERSAL_CONSENT_CONSENT_PURPOSE: {
        //   setBreadcrumbs([
        //     {
        //       component: 'Universal Consent Management',
        //       path: UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
        //     },
        //     {
        //       component: 'Consent Purposes',
        //       path: UNIVERSAL_CONSENT_CONSENT_PURPOSE,
        //     },
        //   ]);
        //   break;
        // }
        case UCM_SUBJECT_CONSENT_TYPES: {
          setBreadcrumbs([
            {
              component: 'Subject Consent Types',
              path: UCM_SUBJECT_CONSENT_TYPES,
            },
          ]);
          break;
        }
        case UCM_SUBJECT_CONSENT_TYPES_DETAILS: {
          setBreadcrumbs([
            {
              component: 'Subject Consent Types',
              path: UCM_SUBJECT_CONSENT_TYPES,
            },
            {
              component: 'Details',
              path: `${UCM_SUBJECT_CONSENT_TYPES_DETAILS}`,
            },
          ]);
          break;
        }

        case `${UCM_SUBJECT_CONSENT_LIST}/${id}`: {
          setBreadcrumbs([
            {
              component: 'Universal Consent Management',
              path: UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Subject Consent List',
              path: UCM_SUBJECT_CONSENT_LIST,
            },
            {
              component: 'Subject Consent Detail',
              path: `${UCM_SUBJECT_CONSENT_LIST}/${id}`,
            },
          ]);
          break;
        }
        case UCM_SUBJECT_CONSENT_LIST: {
          setBreadcrumbs([
            {
              component: 'Universal Consent Management',
              path: UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Subject Consent List',
              path: UCM_SUBJECT_CONSENT_LIST,
            },
          ]);
          break;
        }
        case UCM_SUBJECT_CONSENT_MANAGER: {
          setBreadcrumbs([
            {
              component: 'Subject Consent Manager',
              path: UCM_SUBJECT_CONSENT_MANAGER,
            },
          ]);
          break;
        }
        // case UCM_SUBJECT_CONSENT_MANAGER_TEMPLATE: {
        //   setBreadcrumbs([
        //     {
        //       component: 'Subject Consent Manager',
        //       path: UCM_SUBJECT_CONSENT_MANAGER,
        //     },
        //   ]);
        //   break;
        // }
        // case UCM_SUBJECT_CONSENT_MANAGER_LIST: {
        //   setBreadcrumbs([
        //     {
        //       component: 'Subject Consent Manager',
        //       path: UCM_SUBJECT_CONSENT_MANAGER,
        //     },
        //     {
        //       component: 'Subject Consent Details',
        //       path: UCM_SUBJECT_CONSENT_MANAGER_LIST,
        //     },
        //   ]);
        //   break;
        // }
        case UCM_FORM: {
          setBreadcrumbs([
            {
              component: 'Universal Consent Management',
              path: UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Form',
              path: UCM_FORM,
            },
          ]);
          break;
        }
        case UCM_FORM_CREATE: {
          setBreadcrumbs([
            {
              component: 'Universal Consent Management',
              path: UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Form',
              path: UCM_FORM,
            },
            {
              component: 'Create Form',
              path: UCM_FORM_CREATE,
            },
          ]);
          break;
        }
        case UCM_SOURCE_CONSENT_UPLOAD: {
          setBreadcrumbs([
            {
              component: 'Universal Consent Management',
              path: UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Consent Upload',
              path: UCM_SOURCE_CONSENT_UPLOAD,
            },
          ]);
          break;
        }
        case UCM_TEMPLATES: {
          setBreadcrumbs([
            {
              component: 'Universal Consent Management',
              path: UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
            },

            {
              component: 'Consent Collection Templates',
              path: UCM_TEMPLATES,
            },
          ]);
          break;
        }
        case UCM_ADD_COLLECTION_TEMPLATE: {
          setBreadcrumbs([
            {
              component: 'Universal Consent Management',
              path: UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
            },

            {
              component: 'Consent Collection Templates',
              path: UCM_TEMPLATES,
            },
            {
              component: 'Create Consent Collection Template',
              path: UCM_ADD_COLLECTION_TEMPLATE,
            },
          ]);
          break;
        }
        case UCM_PROCESSING_CATEGORY: {
          setBreadcrumbs([
            {
              component: 'Universal Consent Management',
              path: UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
            },

            {
              component: 'Processing Category',
              path: UCM_PROCESSING_CATEGORY,
            },
          ]);
          break;
        }
        case UCM_PROCESSING_PURPOSE: {
          setBreadcrumbs([
            {
              component: 'Universal Consent Management',
              path: UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
            },

            {
              component: 'Processing Purpose',
              path: UCM_PROCESSING_PURPOSE,
            },
          ]);
          break;
        }
        case UCM_CONSENT_PURPOSE: {
          setBreadcrumbs([
            {
              component: 'Universal Consent Management',
              path: UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
            },

            {
              component: 'Consent Purpose',
              path: UCM_CONSENT_PURPOSE,
            },
          ]);
          break;
        }
        case UCM_PII: {
          setBreadcrumbs([
            {
              component: 'Universal Consent Management',
              path: UNIVERSAL_CONSENT_MANAGEMENT_DASHBOARD,
            },

            {
              component: 'PII Label',
              path: UCM_PII,
            },
          ]);
          break;
        }
        case RETENTION_RULE_LIST: {
          setBreadcrumbs([
            {
              component: 'Retention Rule List',
              path: RETENTION_RULE_LIST,
            },
          ]);
          break;
        }

        case RETENTION_RULE_DETAILS: {
          setBreadcrumbs([
            {
              component: 'Data Retention',
              path: RETENTION_RULE_LIST,
            },

            {
              component: 'Create Retention Rule',
              path: RETENTION_RULE_DETAILS,
            },
          ]);
          break;
        }

        case RETENTION_RULE_DETAILS_VIEW: {
          setBreadcrumbs([
            {
              component: 'Data Retention',
              path: RETENTION_RULE_LIST,
            },

            {
              component: 'View Retention Rule',
              path: RETENTION_RULE_DETAILS_VIEW,
            },
          ]);
          break;
        }
        case DATA_RETENTION_DASHBOARD: {
          setBreadcrumbs([
            {
              component: 'Dashboard',
              path: DATA_RETENTION_DASHBOARD,
            },
          ]);
          break;
        }
        //! Customization
        case CUSTOMIZE: {
          setBreadcrumbs([
            {
              component: 'Customization',
              path: CUSTOMIZE,
            },
          ]);
          break;
        }

        case VISUALIZATION: {
          setBreadcrumbs([
            {
              component: 'Visualization',
              path: VISUALIZATION,
            },
          ]);
          break;
        }

        //! Customer Management

        case CUSOMER_MANAGEMENT: {
          setBreadcrumbs([
            {
              component: 'Customer Management',
              path: CUSOMER_MANAGEMENT,
            },
          ]);
          break;
        }

        case CUSTOMER_MANAGEMENT_ADD_CUSTOMER: {
          setBreadcrumbs([
            {
              component: 'Customer Management',
              path: CUSOMER_MANAGEMENT,
            },

            {
              component: 'Add Customer',
              path: CUSTOMER_MANAGEMENT_ADD_CUSTOMER,
            },
          ]);
          break;
        }

        case CUSTOMER_MANAGEMENT_VIEW_CUSTOMER: {
          setBreadcrumbs([
            {
              component: 'Customer Management',
              path: CUSOMER_MANAGEMENT,
            },

            {
              component: 'View Customer',
              path: CUSTOMER_MANAGEMENT_VIEW_CUSTOMER,
            },
          ]);
          break;
        }

        case CUSTOMER_MANAGEMENT_EDIT_CUSTOMER: {
          setBreadcrumbs([
            {
              component: 'Customer Management',
              path: CUSOMER_MANAGEMENT,
            },

            {
              component: 'Edit Customer',
              path: CUSTOMER_MANAGEMENT_EDIT_CUSTOMER,
            },
          ]);
          break;
        }

        // ! Data Subject Rights Management
        case DATA_SUBJECT_RIGHTS: {
          setBreadcrumbs([
            {
              component: 'Data Subject Rights Management',
              path: DATA_SUBJECT_RIGHTS,
            },
          ]);
          break;
        }
        case DSR_TASK_OVERVIEW: {
          setBreadcrumbs([
            {
              component: 'Data Subject Rights Management',
              path: DATA_SUBJECT_RIGHTS,
            },
            {
              component: 'Requests',
              path: DSR_TASK_OVERVIEW,
            },
          ]);
          break;
        }
        case DSR_TASK_OVERVIEW_VIEW_ASSIGNEE: {
          setBreadcrumbs([
            {
              component: 'Data Subject Rights Management',
              path: DATA_SUBJECT_RIGHTS,
            },
            {
              component: 'Requests',
              path: DSR_TASK_OVERVIEW,
            },
            {
              component: 'View Requests',
              path: DSR_TASK_OVERVIEW_VIEW_ASSIGNEE,
            },
          ]);
          break;
        }
        case DSR_MY_TASK: {
          setBreadcrumbs([
            {
              component: 'Data Subject Rights Management',
              path: DATA_SUBJECT_RIGHTS,
            },
            {
              component: 'Tasks',
              path: DSR_MY_TASK,
            },
          ]);
          break;
        }
        case DSR_MY_TASK_VIEW: {
          setBreadcrumbs([
            {
              component: 'Data Subject Rights Management',
              path: DATA_SUBJECT_RIGHTS,
            },
            {
              component: 'Tasks',
              path: DSR_MY_TASK,
            },
            {
              component: 'View Tasks',
              path: DSR_MY_TASK_VIEW,
            },
          ]);
          break;
        }
        case DSR_TASK_OVERVIEW_APPROVED: {
          setBreadcrumbs([
            {
              component: 'Data Subject Rights Management',
              path: DATA_SUBJECT_RIGHTS,
            },
            {
              component: 'Approved Requests',
              path: DSR_TASK_OVERVIEW_APPROVED,
            },
          ]);
          break;
        }
        case DSR_TASK_OVERVIEW_REJECTED: {
          setBreadcrumbs([
            {
              component: 'Data Subject Rights Management',
              path: DATA_SUBJECT_RIGHTS,
            },
            {
              component: 'Rejected Requests',
              path: DSR_TASK_OVERVIEW_REJECTED,
            },
          ]);
          break;
        }
        case DSR_TASK_OVERVIEW_REJECT_IN_PROGRESS: {
          setBreadcrumbs([
            {
              component: 'Data Subject Rights Management',
              path: DATA_SUBJECT_RIGHTS,
            },
            {
              component: 'Reject in Progress Requests',
              path: DSR_TASK_OVERVIEW_REJECT_IN_PROGRESS,
            },
          ]);
          break;
        }
        case DSR_TASK_OVERVIEW_COMPLETED: {
          setBreadcrumbs([
            {
              component: 'Data Subject Rights Management',
              path: DATA_SUBJECT_RIGHTS,
            },
            {
              component: 'Completed Requests',
              path: DSR_TASK_OVERVIEW_COMPLETED,
            },
          ]);
          break;
        }
        case DSR_TASK_OVERVIEW_ARCHIVED: {
          setBreadcrumbs([
            {
              component: 'Data Subject Rights Management',
              path: DATA_SUBJECT_RIGHTS,
            },
            {
              component: 'Archive Requests',
              path: DSR_TASK_OVERVIEW_ARCHIVED,
            },
          ]);
          break;
        }
        case DSR_TASK_OVERVIEW_CREATE: {
          setBreadcrumbs([
            {
              component: 'Data Subject Rights Management',
              path: DATA_SUBJECT_RIGHTS,
            },
            {
              component: 'Task Overview',
              path: DSR_TASK_OVERVIEW,
            },
            {
              component: 'Create Request',
              path: DSR_TASK_OVERVIEW_CREATE,
            },
          ]);
          break;
        }
        case DSR_EMAIL_TEMPLATES: {
          setBreadcrumbs([
            {
              component: 'Data Subject Rights Management',
              path: DATA_SUBJECT_RIGHTS,
            },
            {
              component: 'Email Templates',
              path: DSR_EMAIL_TEMPLATES,
            },
          ]);
          break;
        }
        case DSR_REQUEST_FORM: {
          setBreadcrumbs([
            {
              component: 'Data Subject Rights Management',
              path: DATA_SUBJECT_RIGHTS,
            },
            {
              component: 'Email Templates',
              path: DSR_EMAIL_TEMPLATES,
            },
            {
              component: 'Create Email Template',
              path: DSR_REQUEST_FORM,
            },
          ]);
          break;
        }
        case DSR_RETENTION_SCHEDULE: {
          setBreadcrumbs([
            {
              component: 'Data Subject Rights Management',
              path: DATA_SUBJECT_RIGHTS,
            },
            {
              component: 'Retention Schedule',
              path: DSR_RETENTION_SCHEDULE,
            },
          ]);
          break;
        }
        case DSR_WORKFLOW_TABLE: {
          setBreadcrumbs([
            {
              component: 'Data Subject Rights Management',
              path: DATA_SUBJECT_RIGHTS,
            },
            {
              component: 'Workflow',
              path: DSR_WORKFLOW_TABLE,
            },
          ]);
          break;
        }
        case DSR_ADD_WORKFLOW: {
          setBreadcrumbs([
            {
              component: 'Data Subject Rights Management',
              path: DATA_SUBJECT_RIGHTS,
            },
            {
              component: 'Workflow',
              path: DSR_WORKFLOW_TABLE,
            },
            {
              component: 'Add Workflow',
              path: DSR_ADD_WORKFLOW,
            },
          ]);
          break;
        }
        case DSR_EDIT_WORKFLOW: {
          setBreadcrumbs([
            {
              component: 'Data Subject Rights Management',
              path: DATA_SUBJECT_RIGHTS,
            },
            {
              component: 'Workflow',
              path: DSR_WORKFLOW_TABLE,
            },
            {
              component: 'Edit Workflow',
              path: DSR_EDIT_WORKFLOW,
            },
          ]);
          break;
        }

        case DSR_MY_REQUEST: {
          setBreadcrumbs([
            {
              component: 'Data Subject Rights Management',
              path: DATA_SUBJECT_RIGHTS,
            },
            {
              component: 'My Request',
              path: DSR_MY_REQUEST,
            },
          ]);
          break;
        }

        case DSR_MY_REQUEST_DETAILS: {
          setBreadcrumbs([
            {
              component: 'Data Subject Rights Management',
              path: DATA_SUBJECT_RIGHTS,
            },
            {
              component: 'My Request',
              path: DSR_MY_REQUEST,
            },
            {
              component: 'My Request View',
              path: DSR_MY_REQUEST_DETAILS,
            },
          ]);
          break;
        }

        case DSR_VIEW_WORKFLOW: {
          setBreadcrumbs([
            {
              component: 'Data Subject Rights Management',
              path: DATA_SUBJECT_RIGHTS,
            },
            {
              component: 'Workflow',
              path: DSR_WORKFLOW_TABLE,
            },
            {
              component: 'View Workflow',
              path: DSR_VIEW_WORKFLOW,
            },
          ]);
          break;
        }
        // Global Workflow

        case GLOBAL_WORKFLOW_TABLE: {
          setBreadcrumbs([
            {
              component: 'Global Workflow',
              path: GLOBAL_WORKFLOW_TABLE,
            },
          ]);
          break;
        }
        case GLOBAL_ADD_WORKFLOW: {
          setBreadcrumbs([
            {
              component: 'Global Workflow',
              path: GLOBAL_WORKFLOW_TABLE,
            },
            {
              component: 'Add Workflow',
              path: GLOBAL_ADD_WORKFLOW,
            },
          ]);
          break;
        }
        case GLOBAL_EDIT_WORKFLOW: {
          setBreadcrumbs([
            {
              component: 'Global Workflow',
              path: GLOBAL_WORKFLOW_TABLE,
            },
            {
              component: 'Edit Workflow',
              path: GLOBAL_EDIT_WORKFLOW,
            },
          ]);
          break;
        }
        case GLOBAL_VIEW_WORKFLOW: {
          setBreadcrumbs([
            {
              component: 'Global Workflow',
              path: GLOBAL_WORKFLOW_TABLE,
            },
            {
              component: 'View Workflow',
              path: GLOBAL_VIEW_WORKFLOW,
            },
          ]);
          break;
        }
        // ! Support
        case SUPPORT: {
          setBreadcrumbs([
            {
              component: 'Support',
              path: SUPPORT,
            },
          ]);
          break;
        }
        case CREATE_TICKET: {
          setBreadcrumbs([
            {
              component: 'Support',
              path: SUPPORT,
            },
            {
              component: 'Create Ticket',
              path: CREATE_TICKET,
            },
          ]);
          break;
        }
        case VIEW_TICKET_DETAILS: {
          setBreadcrumbs([
            {
              component: 'Support',
              path: SUPPORT,
            },
            {
              component: 'View Ticket',
              path: VIEW_TICKET_DETAILS,
            },
          ]);
          break;
        }
        case EDIT_TICKET_DETAILS: {
          setBreadcrumbs([
            {
              component: 'Support',
              path: SUPPORT,
            },
            {
              component: 'Edit Ticket',
              path: EDIT_TICKET_DETAILS,
            },
          ]);
          break;
        }
        // ! Finance
        case INVOICE: {
          setBreadcrumbs([
            {
              component: 'Finance',
              path: INVOICE,
            },
          ]);
          break;
        }
        // !Universal Control Framework
        case UCF: {
          setBreadcrumbs([
            {
              component: 'Universal Control Framework',
              path: UCF,
            },
          ]);
          break;
        }
        case UCF_IMPROVENENT: {
          setBreadcrumbs([
            {
              component: 'Universal Control Framework',
              path: UCF,
            },
            {
              component: 'Improvements',
              path: UCF_IMPROVENENT,
            },
          ]);
          break;
        }

        // !Privacy Ops

        case PRIVACY_OPS_RISK_DASHBOARD: {
          setBreadcrumbs([
            {
              component: 'Risk Dashboard',
              path: PRIVACY_OPS_RISK_DASHBOARD,
            },
          ]);
          break;
        }
        case PRIVACY_OPS_COMPLIANCE_DASHBOARD: {
          setBreadcrumbs([
            {
              component: 'Compliance Dashboard',
              path: PRIVACY_OPS_COMPLIANCE_DASHBOARD,
            },
          ]);
          break;
        }
        case ADD_CONTROL_CATEGORY: {
          setBreadcrumbs([
            {
              component: 'Control Category',
              path: PRIVACY_OPS_CONTROL_HANDBOOK,
            },
            {
              component: 'Add Control Category',
              path: ADD_CONTROL_CATEGORY,
            },
          ]);
          break;
        }
        case PRIVACY_OPS_CONTROL_DETAILS: {
          setBreadcrumbs([
            {
              component: 'Control Category',
              path: PRIVACY_OPS_CONTROL_HANDBOOK,
            },
            {
              component: 'Controls',
              path: PRIVACY_OPS_CONTROL_DETAILS,
            },
          ]);
          break;
        }
        case PRIVACY_OPS_CONTROL_HANDBOOK: {
          setBreadcrumbs([
            {
              component: 'Control Category',
              path: PRIVACY_OPS_CONTROL_HANDBOOK,
            },
          ]);
          break;
        }
        case PRIVACY_OPS_DOCUMENT_REPO: {
          setBreadcrumbs([
            {
              component: 'Privacy Ops',
              path: '/home',
            },
            {
              component: 'Document Repository',
              path: PRIVACY_OPS_DOCUMENT_REPO,
            },
          ]);
          break;
        }
        case PRIVACY_OPS_ASSESSMENT_REPO: {
          setBreadcrumbs([
            {
              component: 'Privacy Ops',
              path: '/home',
            },
            {
              component: 'Assessment Repository',
              path: PRIVACY_OPS_ASSESSMENT_REPO,
            },
          ]);
          break;
        }
        case PRIVACY_OPS_ASSESSMENT_REPO_DETAILS.replace(':id', `${id}`): {
          setBreadcrumbs([
            {
              component: 'Privacy Ops',
              path: '/home',
            },
            {
              component: 'Assessment Repository',
              path: PRIVACY_OPS_ASSESSMENT_REPO,
            },
            {
              component: 'Details',
              path: PRIVACY_OPS_ASSESSMENT_REPO_DETAILS.replace(':id', `${id}`),
            },
          ]);
          break;
        }
        case PRIVACY_OPS_PROCESSING_ACTIVITIES: {
          setBreadcrumbs([
            {
              component: 'Privacy Ops',
              path: '/home',
            },
            {
              component: 'Processing Activities',
              path: PRIVACY_OPS_PROCESSING_ACTIVITIES,
            },
          ]);
          break;
        }
        case PRIVACY_OPS_PROCESSING_ACTIVITIES_DETAILS.replace(':id', `${id}`): {
          setBreadcrumbs([
            {
              component: 'Privacy Ops',
              path: '/home',
            },
            {
              component: 'Processing Activities',
              path: PRIVACY_OPS_PROCESSING_ACTIVITIES,
            },
            {
              component: 'Details',
              path: PRIVACY_OPS_PROCESSING_ACTIVITIES_DETAILS.replace(':id', `${id}`),
            },
          ]);
          break;
        }
        case PRIVACY_OPS_REGULATIONS: {
          setBreadcrumbs([
            {
              component: 'Regulations',
              path: PRIVACY_OPS_REGULATIONS,
            },
          ]);
          break;
        }
        case PRIVACY_OPS_REGULATIONS_DETAILS: {
          setBreadcrumbs([
            {
              component: 'Regulations',
              path: PRIVACY_OPS_REGULATIONS,
            },
            {
              component: `${selectedRegulation}`,
              path: PRIVACY_OPS_REGULATIONS_DETAILS,
            },
          ]);
          break;
        }
        case PRIVACY_OPS_RISK_REGISTER: {
          setBreadcrumbs([
            {
              component: 'Risk Register',
              path: PRIVACY_OPS_RISK_REGISTER,
            },
          ]);
          break;
        }
        case PRIVACY_OPS_RISK_REGISTER_DETAILS: {
          setBreadcrumbs([
            {
              component: 'Risk Register',
              path: PRIVACY_OPS_RISK_REGISTER,
            },
            {
              component: selectedRowID,
              path: PRIVACY_OPS_RISK_REGISTER_DETAILS,
            },
          ]);
          break;
        }
        case PRIVACY_OPS_ACTIVITIES_DUTIES: {
          setBreadcrumbs([
            {
              component: 'Privacy Ops',
              path: '/home',
            },
            {
              component: 'Duty',
              path: PRIVACY_OPS_ACTIVITIES_DUTIES,
            },
          ]);
          break;
        }
        case PRIVACY_OPS_ACTIVITIES_ACTIONS: {
          setBreadcrumbs([
            {
              component: 'Privacy Ops',
              path: '/home',
            },
            {
              component: 'Action',
              path: PRIVACY_OPS_ACTIVITIES_ACTIONS,
            },
          ]);
          break;
        }
        case PRIVACY_OPS_ACTIVITIES_IMPROVEMENTS: {
          setBreadcrumbs([
            {
              component: 'Privacy Ops',
              path: '/home',
            },
            {
              component: 'Improvement Actions',
              path: PRIVACY_OPS_ACTIVITIES_IMPROVEMENTS,
            },
          ]);
          break;
        }
        case PRIVACY_OPS_RISK_REGISTER_AUDIT: {
          setBreadcrumbs([
            {
              component: 'Risk Register',
              path: PRIVACY_OPS_RISK_REGISTER,
            },
            {
              component: 'Audit Log',
              path: PRIVACY_OPS_RISK_REGISTER_AUDIT,
            },
          ]);
          break;
        }

        // Breach management
        case BREACH_MANAGEMENT_DASHBOARD: {
          setBreadcrumbs([
            {
              component: 'Dashboard',
              path: BREACH_MANAGEMENT_DASHBOARD,
            },
          ]);
          break;
        }
        case BREACH_LIST: {
          setBreadcrumbs([
            {
              component: 'Breaches',
              path: BREACH_LIST,
            },
          ]);
          break;
        }
        case BREACH_DETAILS: {
          setBreadcrumbs([
            {
              component: 'Breaches',
              path: BREACH_LIST,
            },
            {
              component: 'Breach Details',
              path: BREACH_DETAILS,
            },
          ]);
          break;
        }
        case POLICY_MANAGEMENT_RETENTIION_POLICY: {
          setBreadcrumbs([
            {
              component: 'Policy Management',
              path: POLICY_MANAGEMENT_DASHBOARD,
            },
            {
              component: 'Retention Policy',
              path: POLICY_MANAGEMENT_RETENTIION_POLICY,
            },
          ]);
          break;
        }
        default: {
          setBreadcrumbs([]);
        }
      }
    }
  }, [window.location.pathname]);

  return (
    <Breadcrumb className={`${styles.main_heading}`}>
      <BreadcrumbList>
        <BreadcrumbItem>
          <BreadcrumbLink asChild>
            <NavLink
              to="/home"
              className={`${breadcrumbs?.length === 0 ? 'font-semibold text-primary-text' : 'font-semibold'} text-sm`}
            >
              {/* {t('Breadcrumbs.ControlRoom')} */}
              {window.location.pathname !== '/home' ? t('Breadcrumbs.ControlRoom') : null}
            </NavLink>
          </BreadcrumbLink>
        </BreadcrumbItem>
        {breadcrumbs?.length ? <BreadcrumbSeparator /> : null}
        {breadcrumbs?.map((item, index: number) => (
          <React.Fragment key={`${item?.component.trim()}_${index}`}>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <NavLink
                  to={item?.path}
                  className={`${index === breadcrumbs?.length - 1 ? 'font-semibold text-primary-text' : 'font-semibold'} text-sm`}
                >
                  {t(`Breadcrumbs.${item?.component}`, item?.component)}
                </NavLink>
              </BreadcrumbLink>
            </BreadcrumbItem>
            {index === breadcrumbs?.length - 1 ? null : <BreadcrumbSeparator />}
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default MainBreadcrumb;
