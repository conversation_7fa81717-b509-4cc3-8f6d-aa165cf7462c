import { ChangeEvent, useCallback, useState } from 'react';

import { debounce } from 'lodash';
import { useTranslation } from 'react-i18next';
import { useAuth } from 'react-oidc-context';
import { useSelector } from 'react-redux';
import { NavLink } from 'react-router-dom';
import { RootState } from '../../redux/store';

interface SideBarData {
  resource_id: number;
  resource_name: string;
  parent_id: number;
  route?: string | null;
  order?: number;
  description?: string | null;
  icon: string;
  children: SideBarData[];
}

const findNavigationRoute = (resources: SideBarData[]): string => {
  for (const resource of resources) {
    if (resource.route) {
      return resource.route;
    }
    if (resource.children) {
      const childRoute: string | null = findNavigationRoute(resource.children);
      if (childRoute) {
        return childRoute;
      }
    }
  }
  return '/home';
};

const Home = () => {
  const auth = useAuth();
  const [searchValue, setSearchValue] = useState('');

  const sideBarData = useSelector((state: RootState) => state.SideBar?.sidebar);

  const handleSearchValue = useCallback(
    debounce((event: ChangeEvent<HTMLInputElement>) => {
      setSearchValue(event.target.value);
    }, 1000), // delay in ms
    []
  );

  console.log('search value: ', searchValue);
  const { t } = useTranslation();

  return (
    <main className="size-full px-4 font-primary-text">
      {/* Header section */}
      {/* <section className="flex flex-col items-center justify-center gap-2 py-5">
        <div className="flex w-full flex-col items-center justify-center">
          <div className="text-base font-medium leading-normal tracking-tight text-ucm-blue">
            Welcome to
          </div>

          <img src={gotrust_logo_with_title} alt="gotrust logo" className="w-[180px]"></img>
        </div>
        <div className="flex h-10 w-[45%] items-center justify-between gap-2 rounded-md border border-solid border-border bg-primary-background px-3">
          <img src={search} alt="search sign" className="size-[19px] shrink-0" />
          <input
            type="text"
            id="searchBar"
            name="searchBar"
            placeholder="What can we help you with?"
            className="w-full bg-transparent font-primary-text text-sm font-normal leading-normal text-[#64748B] outline-none"
            onChange={(e) => handleSearchValue(e)}
          />
        </div>

        <p className="w-1/2 text-base font-normal leading-6 text-ucm-blue">
          Leverage GoTrust comprehensive assessments to simplify compliance implementation and
          management for your business or organisation.
        </p>
      </section> */}

      {/* Resources given to user. */}
      <section className="grid grid-cols-[repeat(auto-fit,minmax(200px,1fr))] gap-4 px-10 py-10">
        {sideBarData?.map((item) => {
          const iconPath = `${import.meta.env.VITE_APP_FRONTEND_URL}/assets/${item.icon.replace('https://go-asset-management-profile-image.s3.us-east-2.amazonaws.com', '')}`;
          if (item.route ?? findNavigationRoute([item]) === 'WORKFLOW') {
            return (
              <NavLink
                key={item?.resource_id}
                to={`${import.meta.env.VITE_WORKFLOW_AUTOMATION_BASE_URL}/authenticate?search=${auth?.user?.access_token}&gt-web=${import.meta.env.VITE_APP_FRONTEND_URL}&gt-app=${import.meta.env.VITE_APP_BASE_API}`}
                target="_blank"
                className="main-card-hover min-w-[200px] rounded-2xl border border-white/30 bg-white/10 p-4 shadow-lg backdrop-blur-lg transition-all duration-300 ease-in-out hover:scale-[1.05] hover:border-white/50 hover:shadow-[0_4px_30px_rgba(255,255,255,0.3)] hover:backdrop-blur-2xl"
              >
                <img src={iconPath} className="aspect-square h-10 w-10" alt="img" />
                <div className="flex h-[110px] w-full items-center justify-between gap-2 border-none hover:bg-transparent">
                  <div className="flex h-full w-[90%] items-center justify-start gap-2">
                    <span className="flex-shrink whitespace-normal break-words text-start font-poppins text-base font-semibold text-white">
                      {t(`Home.${item?.resource_name}`, { defaultValue: item?.resource_name })}
                    </span>
                  </div>
                  {/* <div className="w-[10%] min-w-[50px]">
                    <img
                      src={redirect}
                      className="flex aspect-square size-10 rotate-180"
                      alt="img"
                    />
                  </div> */}
                </div>
                {/* <p className="px-4 pb-2 text-xs font-normal leading-[18px] text-[#5E5E5E]">
                  {item?.description ?? ''}
                </p> */}
              </NavLink>
            );
          } else {
            return (
              <NavLink
                key={item?.resource_id}
                to={item.route ?? findNavigationRoute([item])}
                className="main-card-hover min-w-[200px] rounded-2xl border border-white/30 bg-white/10 from-[#0B101C] from-[length:30%] to-[#0847F7] p-4 shadow-lg backdrop-blur-lg transition-all duration-300 ease-in-out hover:scale-[1.05] hover:border-white/50 hover:shadow-[0_4px_30px_rgba(255,255,255,0.3)] hover:backdrop-blur-2xl"
              >
                <img src={iconPath} className="aspect-square h-10 w-10" alt="img" />
                <div className="h-[110px] w-full items-center justify-between gap-2 border-none hover:bg-transparent">
                  <div className="flex h-full w-full items-center justify-between gap-2">
                    <div className="flex w-full items-center gap-2">
                      <div className="flex-shrink whitespace-normal break-words text-start font-poppins text-base font-semibold text-white">
                        {t(`Home.${item?.resource_name}`, { defaultValue: item?.resource_name })}
                      </div>
                    </div>
                    {/* <div className="w-[10%] min-w-[50px]">
                      <img
                        src={redirect}
                        className="flex aspect-square size-10 rotate-180"
                        alt="img"
                      />
                    </div> */}
                  </div>
                </div>
              </NavLink>
            );
          }
        })}
      </section>
    </main>
  );
};
export default Home;
