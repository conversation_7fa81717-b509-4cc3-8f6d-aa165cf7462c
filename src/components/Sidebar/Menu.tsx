import { DropdownMenuArrow } from '@radix-ui/react-dropdown-menu';
import { ChevronDown, MoreHorizontal } from 'lucide-react';
import { useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from 'react-oidc-context';
import { useSelector } from 'react-redux';
import { NavLink, useLocation } from 'react-router-dom';
import { Button } from '../../@/components/ui/Common/Elements/Button/Button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '../../@/components/ui/Common/Elements/Popover/Popover';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '../../@/components/ui/collapsible';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '../../@/components/ui/dropdown-menu';
import { ScrollArea } from '../../@/components/ui/scroll-area';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '../../@/components/ui/tooltip';
import { getMenuList } from '../../@/lib/menu-list';
import { cn } from '../../@/lib/utils';

type Submenu = {
  href: string;
  label: string;
  active: boolean;
  icon: string;
  submenus: Submenu[];
};

interface CollapseMenuButtonProperties {
  icon: string;
  label: string;
  active: boolean;
  submenus: Submenu[];
  isOpen: boolean | undefined;
  depth: number;
  href: string;
  activeItemRef: React.RefObject<HTMLDivElement | null>;
}

interface MenuProperties {
  isOpen: boolean | undefined;
}

interface MenuItem {
  href: string;
  label: string;
  icon: string;
  active: boolean;
  submenus: MenuItem[];
}
interface RenderMenuItemsProps {
  menus: MenuItem[];
  isOpen: boolean | undefined;
  depth?: number;
  t: any;
  auth: any;
  activeItemRef: React.RefObject<HTMLDivElement | null>;
}

// const hoverEffect = 'hover:bg-custom-primary hover:text-white hover:rounded-lg';
const baseButtonStyles = 'transition-all duration-300 ease-in-out';
const hoverEffect =
  'hover:bg-gradient-to-l hover:from-[#0B101C] hover:from-[length:8%] hover:to-[#0847F7] hover:text-white hover:rounded-lg';
const submenuHoverEffect =
  'hover:bg-gradient-to-l hover:from-[#0B101C] hover:from-[length:8%] hover:to-[#0847F7] hover:text-white hover:rounded-lg';
const activeEffect =
  'bg-gradient-to-l from-[#0B101C] from-[length:8%] to-[#0847f736] text-white rounded-lg';
const submenuActiveEffect =
  'bg-gradient-to-l from-[#0B101C] from-[length:8%] to-[#0847F7] text-white rounded-lg';

const CollapseMenuButton = ({
  icon,
  label,
  active,
  submenus,
  isOpen,
  depth,
  href,
  activeItemRef,
}: CollapseMenuButtonProperties) => {
  const isSubmenuActive = submenus.some((submenu) => submenu.active);
  const [isCollapsed, setIsCollapsed] = useState<boolean>(active || isSubmenuActive);
  const { t } = useTranslation();
  const [isDropDownOpen, setIsDropDownOpen] = useState(false);
  const currentLocation = useLocation();

  return submenus.length > 0 ? (
    isOpen ? (
      <Collapsible open={isCollapsed} onOpenChange={setIsCollapsed} className="w-full">
        <CollapsibleTrigger className="mb-1 [&[data-state=open]>div>div>svg]:rotate-180" asChild>
          <Button
            variant="ghost"
            className={`h-10 w-full justify-start ${baseButtonStyles} ${depth > 0 ? submenuHoverEffect : hoverEffect} ${
              active || isSubmenuActive ? (depth > 0 ? submenuActiveEffect : activeEffect) : ''
            }`}
            style={{ paddingLeft: depth * 10 }}
          >
            <div
              className="flex w-full items-center justify-between"
              ref={active ? activeItemRef : null}
            >
              <div className="flex items-center gap-2">
                <span className="ml-2 mr-4">
                  <img src={icon} alt={label} width={24} height={24} />
                </span>
                <p
                  className={cn(
                    'max-w-[150px] truncate text-primary-background',
                    isOpen ? 'translate-x-0 opacity-100' : '-translate-x-96 opacity-0'
                  )}
                >
                  {t(`SideBar.SideBarData.${label}`, label)}
                </p>
              </div>
              <div
                className={cn(
                  'whitespace-nowrap',
                  isOpen ? 'translate-x-0 opacity-100' : '-translate-x-96 opacity-0'
                )}
              >
                <ChevronDown size={18} className="text-white transition-transform duration-200" />
              </div>
            </div>
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="data-[state=closed]:animate-collapsible-up data-[state=open]:animate-collapsible-down overflow-hidden">
          {submenus?.map((submenu, index) => (
            <div className="my-2" key={index}>
              <CollapseMenuButton
                {...submenu}
                isOpen={isOpen}
                depth={depth + 1}
                href={submenu.href}
                activeItemRef={activeItemRef}
              />
            </div>
          ))}
        </CollapsibleContent>
      </Collapsible>
    ) : (
      <DropdownMenu open={isDropDownOpen} onOpenChange={setIsDropDownOpen}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className={`h-10 w-full justify-start ${baseButtonStyles} ${depth > 0 ? submenuHoverEffect : hoverEffect} ${
              active || isSubmenuActive ? (depth > 0 ? submenuActiveEffect : activeEffect) : ''
            }`}
            onMouseEnter={() => {
              setIsDropDownOpen(true);
            }}
            onMouseLeave={() => {
              setIsDropDownOpen(false);
            }}
          >
            <div ref={active ? activeItemRef : null}>
              <img src={icon} alt={label} width={24} height={24} />
            </div>
          </Button>
        </DropdownMenuTrigger>

        {/* Dropdown Content (Opens on Hover) */}
        <DropdownMenuContent
          side="right"
          sideOffset={0}
          align="start"
          onMouseEnter={() => setIsDropDownOpen(true)}
          onMouseLeave={() => {
            setIsDropDownOpen(false);
          }}
        >
          <DropdownMenuLabel className="max-w-[190px] truncate">{label}</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {submenus.map((submenu, index) => {
            const isActive =
              currentLocation?.pathname?.includes(submenu?.href) &&
              label !== 'Dashboard' &&
              href?.length > 0;
            return (
              <DropdownMenuItem
                key={index}
                asChild
                className={`rounded-sm ${depth > 0 ? submenuHoverEffect : hoverEffect} data-[highlighted]:text-white ${
                  submenu.active || isActive ? submenuActiveEffect : ''
                }`}
              >
                <NavLink className="cursor-pointer" to={submenu.href}>
                  <p className="max-w-[180px] truncate">{submenu.label}</p>
                </NavLink>
              </DropdownMenuItem>
            );
          })}

          <DropdownMenuArrow className="fill-border" />
        </DropdownMenuContent>
      </DropdownMenu>
    )
  ) : (
    <NavLink to={href} className="h-10">
      <Button
        variant="ghost"
        className={`h-12 w-full justify-start ${baseButtonStyles} ${submenuHoverEffect} ${
          active || currentLocation?.pathname?.includes(href) ? submenuActiveEffect : ''
        }`}
        style={{ paddingLeft: depth * 24 }}
      >
        <div
          className="flex h-full w-full items-center justify-between"
          ref={active ? activeItemRef : null}
        >
          <div className="flex h-full w-full items-center">
            <span className="mr-4 flex-shrink-0">
              <img src={icon} alt={label} width={24} height={24} />
            </span>
            {isOpen && (
              <p
                className={cn(
                  'h-auto whitespace-normal break-words text-left text-primary-background',
                  isOpen ? 'translate-x-0 opacity-100' : '-translate-x-96 opacity-0'
                )}
                style={{ maxWidth: '80%' }}
              >
                {t(`SideBar.SideBarData.${label}`, label)}
              </p>
            )}
          </div>
        </div>
      </Button>
    </NavLink>
  );
};

const RenderMenuItems: React.FC<RenderMenuItemsProps> = ({
  menus,
  isOpen,
  depth = 0,
  t,
  auth,
  activeItemRef,
}) => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const currentLocation = useLocation();
  return (
    <>
      {menus.map((menu, index) => {
        const { href, label, icon, active, submenus } = menu;

        const isActive =
          currentLocation?.pathname?.includes(href) && label !== 'Dashboard' && href?.length > 0;

        let newURL = '';
        switch (href) {
          case 'WORKFLOW': {
            newURL = `${import.meta.env.VITE_WORKFLOW_AUTOMATION_BASE_URL}/authenticate?search=${auth?.user?.access_token}&gt-web=${import.meta.env.VITE_APP_FRONTEND_URL}&gt-app=${import.meta.env.VITE_APP_BASE_API}`;
            break;
          }
          case 'DISCOVERY': {
            const baseUrl = import.meta.env.VITE_APP_FRONTEND_URL;
            newURL = baseUrl.replace('://', '://dd-');
            break;
          }
          default: {
            newURL = href;
            break;
          }
        }

        const imgpath = `${import.meta.env.VITE_APP_FRONTEND_URL}/assets${icon
          .replace('https://go-asset-management-profile-image.s3.us-east-2.amazonaws.com', '')
          .replace('https://go-asset-management-profile-image.s3.amazonaws.com', '')}`;

        const hasSubmenus = submenus && submenus.length > 0;
        const isSubmenuActive = hasSubmenus && submenus.some((submenu) => submenu.active);

        const handleMouseEnter = () => setHoveredIndex(index);
        const handleMouseLeave = () => setHoveredIndex(null);

        const itemContent = (
          <div className="flex items-center gap-2">
            {icon && (
              <img
                src={imgpath}
                className={cn(isOpen === false ? '' : 'ml-2 mr-4')}
                alt={label}
                width={24}
                height={24}
              />
            )}
            {isOpen && (
              <p className="max-w-[200px] truncate text-primary-background">
                {t(`SideBar.SideBarData.${label}`, label)}
              </p>
            )}
          </div>
        );

        const itemClassName = cn('mb-1 flex h-10 w-full items-center');

        if (!hasSubmenus) {
          return (
            <button className="w-full" key={index}>
              <Popover
                open={hoveredIndex === index}
                onOpenChange={(open) => setHoveredIndex(open ? index : null)}
              >
                <div onMouseEnter={handleMouseEnter} onMouseLeave={handleMouseLeave}>
                  <PopoverTrigger asChild>
                    {href === 'WORKFLOW' || href === 'DISCOVERY' ? (
                      <NavLink to={newURL} className={itemClassName} target="_blank">
                        <Button
                          variant="ghost"
                          className={`w-full ${isOpen ? 'justify-start' : 'justify-center'} px-0 ${baseButtonStyles} ${submenuHoverEffect} ${active ? submenuActiveEffect : ''}`}
                          asChild
                        >
                          <div ref={active ? activeItemRef : null}>{itemContent}</div>
                        </Button>
                      </NavLink>
                    ) : (
                      <NavLink to={href} className={itemClassName}>
                        <Button
                          variant="ghost"
                          className={`w-full ${isOpen ? 'justify-start' : 'justify-center'} px-0 ${baseButtonStyles} ${submenuHoverEffect} ${active || isActive ? submenuActiveEffect : ''}`}
                          asChild
                        >
                          <div ref={active ? activeItemRef : null}>{itemContent}</div>
                        </Button>
                      </NavLink>
                    )}
                  </PopoverTrigger>
                  {!isOpen && hoveredIndex === index && (
                    <PopoverContent
                      side="right"
                      className={`w-48 rounded-lg border-2 border-white p-2 shadow-none outline-none ${submenuHoverEffect} data-[highlighted]:text-white ${active ? submenuActiveEffect : ''}`}
                    >
                      <NavLink className="cursor-pointer" to={href}>
                        <p className="max-w-[180px] truncate">{label}</p>
                      </NavLink>
                    </PopoverContent>
                  )}
                </div>
              </Popover>
            </button>
          );
        }

        // For menu with submenus
        return (
          <div className="w-full" key={index} style={{ paddingLeft: depth * 10 }}>
            <CollapseMenuButton
              icon={icon}
              label={label}
              active={active || isSubmenuActive}
              submenus={submenus}
              isOpen={isOpen}
              depth={depth}
              href={href}
              activeItemRef={activeItemRef}
            />
          </div>
        );
      })}
    </>
  );
};

export function Menu({ isOpen }: MenuProperties) {
  const location = useLocation();
  const pathname = location.pathname;
  const sidebar = useSelector((state: any) => state.SideBar?.sidebar);
  const menuList = getMenuList(pathname, sidebar);
  const { t } = useTranslation();
  const auth = useAuth();
  const activeItemRef = useRef<HTMLDivElement>(null);

  // Add effect to scroll to active item
  useEffect(() => {
    if (activeItemRef.current) {
      activeItemRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  }, [pathname]);

  return (
    <ScrollArea className="[&>div>div[style]]:!block">
      <nav className="size-full font-primary-text">
        <div className="flex min-h-[calc(100vh-48px-36px-16px-32px)] flex-col items-start space-y-1 px-2 lg:min-h-[calc(100vh-32px-40px-32px)]">
          {menuList?.map(({ groupLabel, menus }, index) => (
            <div className={cn('w-full', groupLabel ? 'pt-5' : '')} key={index}>
              {/* Add groupLabel as a heading */}
              {(isOpen && groupLabel) || isOpen === undefined ? (
                <p className="max-w-[248px] truncate pb-2 text-sm font-medium text-muted-foreground">
                  {t(`SideBar.GroupLabel.${groupLabel}`, groupLabel)}
                </p>
              ) : !isOpen && isOpen !== undefined && groupLabel ? (
                <TooltipProvider>
                  <Tooltip delayDuration={100}>
                    <TooltipTrigger className="w-full">
                      <div className="flex w-full items-center justify-center">
                        <MoreHorizontal className="size-5" />
                      </div>
                    </TooltipTrigger>
                    <TooltipContent side="right">
                      <p>{t(`SideBar.GroupLabel.${groupLabel}`, groupLabel)}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ) : (
                <p className="pb-2"></p>
              )}
              <RenderMenuItems
                menus={menus}
                isOpen={isOpen}
                depth={0}
                t={t}
                auth={auth}
                activeItemRef={activeItemRef}
              />
            </div>
          ))}
        </div>
      </nav>
    </ScrollArea>
  );
}
