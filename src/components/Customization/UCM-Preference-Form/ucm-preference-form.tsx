import axios from 'axios';
import React, { ChangeEvent, SetStateAction, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Badge } from '../../../@/components/ui/badge';
import { Button } from '../../../@/components/ui/Common/Elements/Button/ButtonSort';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '../../../@/components/ui/Common/Elements/Select/Select';
import { Tabs, TabsList, TabsTrigger } from '../../../@/components/ui/Common/Elements/Tabs/Tabs';
import logoDark from '../../../assets/GoTrustLogo.svg';
import { convertString } from '../../../components/common/CommonHelperFunctions';
import ErrorPage from '../../../components/common/ErrorPage';
import { RootState } from '../../../redux/store';
import { FormConfiguration } from '../../../types/universal-consent-management';
import { get_supported_languages } from '../../common/services/universal-consent-management';
import PrivacyNoticeReaderView from '../../UniversalConsentMangement/Common/privacy-notice-reader-view';
import styles from './ucm-preference-form.module.css';

interface PreferenceFormData {
  additional_info?: AdditionalInfo;
  basic_info?: BasicInfo;
  preference_center_configuration?: ConfigurationProperties;
  privacy_note?: PrivacySection[];
  dsr?: Dsr;
  ucm_preference_form: UcmPreferenceForm[];
}

interface AdditionalInfo {
  preference_center_id: number;
  customer_id: number;
  subject_identity_type_id: number;
  subject_identity_type_name: string;
  entity_id: number;
  url: string;
  status: boolean;
  verification_key: string;
  redirect_URL: string | null;
  is_published: boolean;
  published_at: string; // Date string
  created_at: string; // Date string
  updated_at: string; // Date string
}

interface BasicInfo {
  title: string;
  description: string;
  privacy_note_version: number;
}

interface PrivacySection {
  id: string;
  title: string;
  content: string;
}

interface Dsr {
  need_dsr_center: boolean;
  dsr_content: string | null;
}

interface UcmPreferenceForm {
  customer_id: number;
  collection_template_id: number;
  collection_template_name: string;
  data_principal_id: number;
  purpose_list: Purpose[];
}

interface Purpose {
  processing_purpose_id: number;
  processing_purpose_name: string;
  consent_bucket: ConsentBucket[];
}

export interface ConsentBucket {
  consent_purpose_id: number;
  consent_purpose_name: string;
  consent_purpose_description: string;
  pii_labels: string[];
  vendors: string[];
  processes: string[];
  ct_cp_map_bucket: CTCPMapBucket[];
}

export interface CTCPMapBucket {
  id: number;
  collection_template_id: number;
  processing_purpose_id: number;
  processing_purpose_name: string;
  consent_purpose_id: number;
  consent_purpose_name: string;
  pii_label_id: number;
  pii_label: string;
  frequency: string | null;
  consent_lifetime: number;
  consent_expiration: string | null;
  default_opt_out_allowed: boolean;
  compulsory_consent: boolean;
  consent_status: boolean;
  backend_consent_status: boolean | null;
  manual_revoke_bool: boolean;
  manual_grant_bool: boolean;
  vendor_id: number;
  vendor_name: string;
  process_id: number | null;
  process_name: string | null;
}

interface FrequencyProperties {
  key: string;
  value: string;
  name: string;
}
interface SubjectIdentityDetailsProperties {
  subject_identity_type_id: number;
  subject_identity_type_name: string;
}

interface ConfigurationProperties {
  fontFamily: string;
  logoUrl: string;
  showLogo: boolean;
  showCookie: boolean;
  showConsent: boolean;
  showConsentFlow: boolean;
  showDSR: boolean;
  title: string;
  description: string;
  privacy_notice_heading: string;
  preference_center_heading: string;
  dsr_center_heading: string;
  consent_flow_heading: string;
  dsrURL: string;
  dpoEmail: string;
  dsrContent: string;
  consent_purpose_configuration: FormConfiguration;
}

interface PreferenceFormProperties {
  customerId: number | undefined;
  pfId: number;
  setConfig: React.Dispatch<React.SetStateAction<ConfigurationProperties>>;
  config: ConfigurationProperties;
  preferenceFormData: PreferenceFormData | undefined;
  consentStatus: Map<string, CTCPMapBucket[]>;
  allConsents: any[];
  allChecked: boolean;
  setConsentStatus: React.Dispatch<SetStateAction<Map<string, CTCPMapBucket[]>>>;
}

const UCMPreferenceForm: React.FC<PreferenceFormProperties> = ({
  customerId,
  pfId,
  setConfig,
  config,
  preferenceFormData,
  consentStatus,
  allConsents,
  allChecked,
  setConsentStatus,
}) => {
  //! Variables
  const tabData = [
    config?.privacy_notice_heading ?? 'Privacy Notice',
    ...(config.showConsent ? [config?.preference_center_heading ?? 'Consent Preference'] : []),
    ...(config.showDSR ? [config?.dsr_center_heading ?? 'Data Subject Rights'] : []),
    ...(config.showCookie ? ['Cookie Preference'] : []),
    ...(config.showConsentFlow ? [config?.consent_flow_heading ?? 'Consent Flow'] : []),
  ];
  const badgeClass = 'text-[white] bg-primary';
  const sections: PrivacySection[] = useSelector(
    (state: RootState) => state.UCM.CollectionTemplateData.sections
  );

  const dispatch = useDispatch();

  // ! STATES
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('0');
  const [selectedSectionIndex, setSelectedSectionIndex] = useState<number | null>(null);
  const [frequencyList, setFrequencyList] = useState<FrequencyProperties[]>([]);
  const [ip, setIP] = useState('');
  const [countryCode, setCountryCode] = useState<string>('');
  const [continent, setContinent] = useState<string>('');
  const [languages, setLanguages] = useState([]);
  const [selectedLanguage, setSelectedLanguage] = useState<string>('en');
  // New state to track selected frequency per consent
  const [consentFrequency, setConsentFrequency] = useState<{ [key: number]: string }>({});
  //! LOGS

  //! EFFECTS

  // fetching ip and country code
  useEffect(() => {
    const getData = async () => {
      const response = await axios.get('https://api.ipify.org/?format=json');
      setIP(response.data.ip);
    };

    const getCountry = async () => {
      const response = await axios.get(
        'https://api.ipdata.co?api-key=295ad47616f5cc86434a4a501a2b9dba7eb6aeec63a70e09620aab10'
      );
      setCountryCode(response?.data?.country_code);
      setContinent(response?.data?.continent_name);
    };

    getData();
    getCountry();
  }, []);

  // // Fetching preference data
  // useEffect(() => {
  //   const fetchPreference = async () => {
  //     try {
  //       const responseData = await get_preference_center_data(customerId, pfId);
  //       console.log(responseData);
  //       dispatch(
  //         universalConsentManagementActions.setPreferenceTitle(
  //           responseData.result.data.basic_info.title
  //         )
  //       );
  //       dispatch(
  //         universalConsentManagementActions.setPreferenceDescription(
  //           responseData.result.data.basic_info.description
  //         )
  //       );
  //       const formData: PreferenceFormData = responseData?.result?.data;
  //       setConfig((prev) => {
  //         return {
  //           ...prev,
  //           title: formData?.basic_info?.title,
  //           description: formData?.basic_info?.description,
  //         };
  //       });
  //       setPreferenceFormData(formData);
  //       if (formData?.preference_center_configuration) {
  //         setConfig(formData.preference_center_configuration);
  //       }
  //       // Populate consentStatus state based on response data
  //       formData?.ucm_preference_form?.length > 0 &&
  //         formData?.ucm_preference_form[0]?.purpose_list?.forEach((purpose) => {
  //           purpose.consent_bucket.forEach((consent) => {
  //             setConsentStatus((prev) => {
  //               const updatedMap = new Map(prev);

  //               updatedMap.set(consent.consent_purpose_id, consent?.ct_cp_map_bucket);

  //               return updatedMap;
  //             });
  //           });
  //         });
  //     } catch (error) {
  //       console.error(error);
  //     }
  //   };

  //   if (pfId !== -1) fetchPreference();
  // }, [customerId, pfId]);

  //fetching Supported Languages
  useEffect(() => {
    const fetchSupportedLanguages = async () => {
      try {
        // Call the returned function to fetch data
        const responseData = await get_supported_languages(
          'preference_center',
          Number(customerId),
          undefined,
          Number(pfId),
          undefined
        );
        setLanguages(responseData?.result?.data);
      } catch (error) {
        console.error(error);
      }
    };

    fetchSupportedLanguages();
  }, [customerId, pfId]);

  // fetching frequency
  useEffect(() => {
    const fetchFrequency = async () => {
      try {
        // Call the returned function to fetch data
        const response = await axios.get(
          `${import.meta.env.VITE_APP_GO_TRUST_UNIVERSAL_BASE_API}/api/v2/ct/frequency-list`
        );
        setFrequencyList(response?.data?.result?.data);
      } catch (error) {
        console.error(error);
      }
    };

    fetchFrequency();
  }, [customerId, pfId]);

  // Set first section as default if sections are available
  useEffect(() => {
    if (preferenceFormData?.privacy_note && preferenceFormData.privacy_note.length > 0) {
      setSelectedSectionIndex(0);
    }
  }, [preferenceFormData?.privacy_note]);

  //! Logs
  // console.log(customerId, "->", pfId, "->", userInput);
  // console.log('->', subjectIdentityDetails);

  const handleFrequencyChange = (uniqueId: string, newFrequency: string) => {
    // setConsentFrequency((prev) => ({
    //   ...prev,
    //   [consentId]: newFrequency,
    // }));

    // Also update the frequency in consentStatus
    setConsentStatus((prev) => {
      // Clone the existing Map (React state requires immutability)
      const updatedMap = new Map(prev);
      let updatedValue: CTCPMapBucket[] | undefined = updatedMap?.get(uniqueId);

      if (updatedValue) {
        updatedValue = updatedValue.map((item) => ({
          ...item,
          frequency: newFrequency?.toLowerCase(),
        }));

        updatedMap.set(uniqueId, updatedValue);
      }

      return updatedMap; // Update state with new Map
    });
  };

  const handleConsentChangeAll = (collection_template_id: number, checked: boolean) => {
    setConsentStatus((prev) => {
      const updatedMap = new Map<string, CTCPMapBucket[]>();
      // For every consent purpose (key) in your Map, update the consent_status for each bucket entry.
      prev.forEach((consentList, consentId) => {
        const updatedConsentList = consentList.map((item) => {
          if (!item.compulsory_consent && item.collection_template_id === collection_template_id) {
            return {
              ...item,
              consent_status: checked,
            };
          } else {
            return item;
          }
        });
        updatedMap.set(consentId, updatedConsentList);
      });
      return updatedMap;
    });
  };

  const handleSectionSelect = (index: number) => {
    setSelectedSectionIndex(index);
  };

  const handleConsentChange = (uniqueId: string, checked: boolean) => {
    setConsentStatus((prev) => {
      // Clone the existing Map (React state requires immutability)
      const updatedMap = new Map(prev);
      let updatedValue: CTCPMapBucket[] | undefined = updatedMap?.get(uniqueId);

      if (updatedValue) {
        updatedValue = updatedValue.map((item) => ({
          ...item,
          consent_status: checked,
        }));

        updatedMap.set(uniqueId, updatedValue);
      }
      // Update existing entry OR add a new one

      return updatedMap; // Update state with new Map
    });
  };

  const renderSectionContent = (section: PrivacySection) => {
    if (!section) return <div>Section Not Available</div>;

    return (
      <div className="mb-8 text-sm">
        <h2 className="mb-2 font-semibold">{section.title}</h2>
        <p className="mb-2 text-gray-700">{section.content}</p>
      </div>
    );
  };

  // Conditionally render ErrorPage if there's an error
  if (error) {
    return <ErrorPage errorMessage={error} showFooter={false} />;
  }

  return (
    <main
      className="flex h-full w-full flex-col gap-5 rounded-lg border border-primary-border p-6"
      style={{ fontFamily: config?.fontFamily }}
    >
      <div className="flex w-full flex-col gap-2">
        <header className="flex h-fit w-full flex-row items-center justify-between gap-4">
          {config?.showLogo && (
            <img
              src={config?.logoUrl ?? logoDark}
              alt="logo"
              style={{
                width: config?.consent_purpose_configuration?.form?.logo?.width,
                height: config?.consent_purpose_configuration?.form?.logo?.height,
                objectFit: 'contain',
              }}
            />
          )}
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              width: 'fit-content',
              flexDirection: 'row',
              gap: '10px',
            }}
          >
            {' '}
            <select
              value={selectedLanguage}
              onChange={(event: React.ChangeEvent<HTMLSelectElement>) =>
                setSelectedLanguage(event.target.value)
              }
              className="h-fit w-fit rounded-lg bg-gray-300 p-2"
            >
              <option value="en" key="en">
                English
              </option>
              {languages?.map((lang: { language_code: string; language: string }) => (
                <option value={lang.language_code} key={lang.language_code}>
                  {lang.language}
                </option>
              ))}
            </select>
            <button className={styles.closeButton}>X</button>
          </div>
        </header>
        <div className="flex w-full flex-row items-center justify-between gap-2">
          <p className="h-full max-w-[80%] text-2xl font-semibold">{config?.title}</p>
        </div>

        <p className="">{config?.description}</p>
      </div>

      <div className="w-full">
        <Tabs
          value={activeTab}
          onValueChange={(currentTab) => setActiveTab(currentTab)}
          className="h-auto w-1/3 min-w-[600px] flex-wrap items-center rounded-lg bg-[#f6f6f6] p-1"
          style={{
            borderRadius: config?.consent_purpose_configuration?.form?.border_radius,
          }}
        >
          <TabsList className="flex size-full flex-row items-center justify-between">
            {tabData?.map((item, index) => (
              <Button variant="ghost" className="w-full p-0">
                <TabsTrigger
                  value={index.toString()}
                  className={`${activeTab === index.toString() ? 'text-white' : ''} w-full`}
                  style={{
                    backgroundColor:
                      activeTab === index.toString()
                        ? `${config?.consent_purpose_configuration?.form?.color_scheme}`
                        : 'transparent',
                    color: activeTab === index.toString() ? `white` : 'gray',
                    borderRadius: config?.consent_purpose_configuration?.form?.border_radius,
                  }}
                >
                  {item}
                </TabsTrigger>
              </Button>
            ))}
          </TabsList>
        </Tabs>
      </div>

      <section
        className={`w-full overflow-auto ${styles.table_main_content}`}
        style={{ height: 'calc(100vh - 300px)' }}
      >
        {activeTab === '0' && <PrivacyNoticeReaderView sections={sections} />}

        {activeTab === '1' && (
          <div className="flex h-full w-full flex-col gap-5">
            {preferenceFormData?.ucm_preference_form?.map((item) => (
              <div
                style={{ display: 'flex', flexDirection: 'column', gap: '12px', width: '100%' }}
                key={item?.collection_template_id}
              >
                <p style={{ fontSize: '18px' }}>{convertString(item?.collection_template_name)}</p>
                <div
                  style={{ display: 'flex', flexDirection: 'column', gap: '8px', width: '100%' }}
                >
                  {config?.consent_purpose_configuration?.consent_collection_section?.heading
                    ?.text && (
                    <p
                      style={{
                        fontWeight: 'bold',
                        fontSize:
                          config?.consent_purpose_configuration?.consent_collection_section?.heading
                            ?.size,
                        fontFamily: config?.consent_purpose_configuration?.form?.font_family,
                        color:
                          config?.consent_purpose_configuration?.consent_collection_section?.heading
                            ?.color,
                      }}
                    >
                      {
                        config?.consent_purpose_configuration?.consent_collection_section?.heading
                          ?.text
                      }
                    </p>
                  )}
                  {config?.consent_purpose_configuration?.consent_collection_section?.description
                    ?.text && (
                    <p
                      style={{
                        fontSize:
                          config?.consent_purpose_configuration?.consent_collection_section
                            ?.description?.size,
                        fontFamily: config?.consent_purpose_configuration?.form?.font_family,
                        color:
                          config?.consent_purpose_configuration?.consent_collection_section
                            ?.description?.color,
                      }}
                    >
                      {
                        config?.consent_purpose_configuration?.consent_collection_section
                          ?.description?.text
                      }
                    </p>
                  )}
                  {config?.consent_purpose_configuration?.consent_collection_section
                    ?.show_check_all_checkbox ? (
                    <div>
                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'row',
                          alignItems: 'start',
                          gap: '8px',
                        }}
                      >
                        <input
                          type="checkbox"
                          style={{
                            marginTop: '4px',
                            height: '18px',
                            width: '18px',
                            cursor: 'pointer',
                            accentColor: config?.consent_purpose_configuration?.form?.color_scheme,
                            borderRadius:
                              config?.consent_purpose_configuration?.form?.border_radius,
                          }}
                          defaultChecked={
                            consentStatus?.get(
                              `${item?.purpose_list[0]?.consent_bucket[0]?.consent_purpose_id}_${item?.collection_template_id}`
                            )?.[0]?.consent_status
                          }
                          checked={
                            consentStatus?.get(
                              `${item?.purpose_list[0]?.consent_bucket[0]?.consent_purpose_id}_${item?.collection_template_id}`
                            )?.[0]?.consent_status
                          }
                          onChange={(event: ChangeEvent<HTMLInputElement>) =>
                            handleConsentChangeAll(
                              item?.collection_template_id,
                              event.target.checked
                            )
                          }
                          id={item?.collection_template_id?.toString()}
                        />
                        <p
                          style={{
                            fontFamily: config?.consent_purpose_configuration?.form?.font_family,
                            fontSize:
                              config?.consent_purpose_configuration?.consent_collection_section
                                ?.description?.size,
                          }}
                        >
                          {
                            config?.consent_purpose_configuration?.consent_collection_section
                              ?.all_checkbox_text
                          }
                        </p>
                      </div>
                      <ul style={{ listStyleType: 'disc', paddingLeft: '2.5rem' }}>
                        {item?.purpose_list?.map((purpose) => (
                          <>
                            {/* Nested list for consent buckets */}
                            {purpose.consent_bucket.map((consent) => (
                              <li key={consent.consent_purpose_id} style={{ marginBottom: '1rem' }}>
                                {/* Wrap your flex layout in a child div */}
                                <div
                                  style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}
                                >
                                  <p style={{ margin: 0 }}>
                                    <strong>{consent.consent_purpose_name}</strong>
                                  </p>
                                  <p style={{ margin: 0 }}>{consent.consent_purpose_description}</p>
                                  {/* PII Labels */}
                                  {consent?.pii_labels?.length > 0 &&
                                    config?.consent_purpose_configuration?.pii_section
                                      ?.show_badges && (
                                      <div
                                        style={{
                                          display: 'flex',
                                          flexDirection: 'row',
                                          flexWrap: 'wrap',
                                          gap: '8px',
                                        }}
                                      >
                                        <span>PII:</span>
                                        {consent.pii_labels.map((label, index) =>
                                          label ? (
                                            <Badge
                                              key={index}
                                              style={{
                                                color: 'white',
                                                backgroundColor:
                                                  config?.consent_purpose_configuration?.form
                                                    ?.color_scheme,
                                              }}
                                            >
                                              {label}
                                            </Badge>
                                          ) : null
                                        )}
                                      </div>
                                    )}
                                  {/* Vendors */}
                                  {consent?.vendors?.length > 0 &&
                                    config?.consent_purpose_configuration?.pii_section
                                      ?.show_badges && (
                                      <div
                                        style={{
                                          display: 'flex',
                                          flexDirection: 'row',
                                          flexWrap: 'wrap',
                                          gap: '8px',
                                        }}
                                      >
                                        <span>Vendor:</span>
                                        {consent.vendors.map((vendor, index) =>
                                          vendor ? (
                                            <Badge
                                              key={index}
                                              style={{
                                                color: 'white',
                                                backgroundColor:
                                                  config?.consent_purpose_configuration?.form
                                                    ?.color_scheme,
                                              }}
                                            >
                                              {vendor}
                                            </Badge>
                                          ) : null
                                        )}
                                      </div>
                                    )}
                                  {/* Processes */}
                                  {consent?.processes?.length > 0 &&
                                    config?.consent_purpose_configuration?.pii_section
                                      ?.show_badges && (
                                      <div
                                        style={{
                                          display: 'flex',
                                          flexDirection: 'row',
                                          flexWrap: 'wrap',
                                          gap: '8px',
                                        }}
                                      >
                                        <span>Process:</span>
                                        {consent.processes.map((process, index) =>
                                          process ? (
                                            <Badge
                                              key={index}
                                              style={{
                                                color: 'white',
                                                backgroundColor:
                                                  config?.consent_purpose_configuration?.form
                                                    ?.color_scheme,
                                              }}
                                            >
                                              {process}
                                            </Badge>
                                          ) : null
                                        )}
                                      </div>
                                    )}
                                </div>
                              </li>
                            ))}
                          </>
                        ))}
                      </ul>
                    </div>
                  ) : (
                    <>
                      {item?.purpose_list.map((purpose) => (
                        <div key={purpose.processing_purpose_id}>
                          {purpose?.processing_purpose_name && (
                            <p
                              style={{
                                fontSize: '16px',
                                fontWeight: 'semibold',
                                color: '#000000',
                              }}
                            >
                              {purpose.processing_purpose_name}
                            </p>
                          )}
                          {purpose.consent_bucket.map((consent) => (
                            <div
                              key={consent.consent_purpose_id}
                              style={{
                                marginTop: '8px',
                                display: 'grid',
                                width: '100%',
                                alignItems: 'start',
                                gap: '16px',
                                gridTemplateColumns: consent.ct_cp_map_bucket[0]?.frequency
                                  ? '4fr 1fr'
                                  : '1fr',
                              }}
                            >
                              <div
                                style={{
                                  display: 'flex',
                                  flexDirection: 'row',
                                  width: '100%',
                                  alignItems: 'flex-start',
                                  gap: '8px',
                                }}
                              >
                                <div
                                  style={{
                                    display: 'flex',
                                    alignItems: 'center',
                                  }}
                                >
                                  <input
                                    style={{
                                      height: '16px',
                                      width: '16px',
                                      marginTop: '3px',
                                      cursor:
                                        consent.ct_cp_map_bucket[0]?.default_opt_out_allowed &&
                                        consent.ct_cp_map_bucket[0]?.compulsory_consent
                                          ? 'not-allowed'
                                          : 'pointer',
                                      accentColor:
                                        config?.consent_purpose_configuration?.form?.color_scheme,
                                      borderRadius:
                                        config?.consent_purpose_configuration?.form?.border_radius,
                                    }}
                                    defaultChecked={
                                      consentStatus?.get(
                                        `${consent?.consent_purpose_id}_${item?.collection_template_id}`
                                      )?.[0]?.consent_status
                                        ? true
                                        : consent.ct_cp_map_bucket[0]?.default_opt_out_allowed
                                          ? true
                                          : false
                                    }
                                    type="checkbox"
                                    checked={
                                      consentStatus?.get(
                                        `${consent?.consent_purpose_id}_${item?.collection_template_id}`
                                      )?.[0]?.consent_status
                                    }
                                    onChange={(event: ChangeEvent<HTMLInputElement>) =>
                                      handleConsentChange(
                                        `${consent?.consent_purpose_id}_${item?.collection_template_id}`,
                                        event.target.checked
                                      )
                                    }
                                    required={consent.ct_cp_map_bucket[0]?.compulsory_consent}
                                    disabled={
                                      consent.ct_cp_map_bucket[0]?.default_opt_out_allowed &&
                                      consent.ct_cp_map_bucket[0]?.compulsory_consent
                                    }
                                    id={consent?.consent_purpose_id?.toString()}
                                  />
                                  <label
                                    htmlFor={consent?.consent_purpose_id?.toString()}
                                    className="text-sm text-gray-700"
                                  >
                                    {consent?.ct_cp_map_bucket[0]?.compulsory_consent && (
                                      <span className="ml-1 text-red-500">*</span>
                                    )}
                                  </label>
                                </div>

                                <div className="flex flex-col flex-wrap gap-2">
                                  <p className="flex flex-col gap-2">
                                    <p
                                      style={{
                                        fontSize: '16px',
                                        fontWeight: 'semibold',
                                        color: '#000000',
                                      }}
                                    >
                                      {consent.consent_purpose_name}
                                    </p>
                                    <p>{consent.consent_purpose_description}</p>
                                  </p>
                                  {/* PII Labels */}
                                  {consent?.pii_labels?.length > 0 &&
                                    config?.consent_purpose_configuration?.pii_section
                                      ?.show_badges && (
                                      <p className="flex w-full flex-row flex-wrap gap-2">
                                        <p>PII:</p>
                                        {consent?.pii_labels?.map((consent) => {
                                          if (consent) {
                                            return (
                                              <Badge
                                                style={{
                                                  color: 'white',
                                                  backgroundColor:
                                                    config?.consent_purpose_configuration?.form
                                                      ?.color_scheme,
                                                }}
                                              >
                                                {consent}
                                              </Badge>
                                            );
                                          }
                                          return <></>;
                                        })}
                                      </p>
                                    )}
                                  {/* Vendors */}
                                  {consent?.vendors?.length > 0 &&
                                    config?.consent_purpose_configuration?.pii_section
                                      ?.show_badges && (
                                      <p className="flex w-full flex-row flex-wrap gap-2">
                                        <p>Vendor:</p>
                                        {consent?.vendors?.map((consent) => {
                                          if (consent) {
                                            return (
                                              <Badge
                                                style={{
                                                  color: 'white',
                                                  backgroundColor:
                                                    config?.consent_purpose_configuration?.form
                                                      ?.color_scheme,
                                                }}
                                              >
                                                {consent}
                                              </Badge>
                                            );
                                          }
                                          return <></>;
                                        })}
                                      </p>
                                    )}
                                  {/* Processes */}
                                  {consent?.processes?.length > 0 &&
                                    config?.consent_purpose_configuration?.pii_section
                                      ?.show_badges && (
                                      <p className="flex w-full flex-row flex-wrap gap-2">
                                        <p>Process:</p>
                                        {consent?.processes?.map((consent) => {
                                          if (consent) {
                                            return (
                                              <Badge
                                                style={{
                                                  color: 'white',
                                                  backgroundColor:
                                                    config?.consent_purpose_configuration?.form
                                                      ?.color_scheme,
                                                }}
                                              >
                                                {consent}
                                              </Badge>
                                            );
                                          }
                                          return <></>;
                                        })}
                                      </p>
                                    )}
                                </div>
                              </div>

                              {consent.ct_cp_map_bucket[0]?.frequency && (
                                <div
                                  style={{
                                    display: 'flex',
                                    flexDirection: 'row',
                                    alignItems: 'center',
                                    gap: '8px',
                                    width: '100%',
                                    paddingRight: '10px',
                                  }}
                                >
                                  <label
                                    style={{
                                      width: 'auto',
                                    }}
                                  >
                                    Frequency:
                                  </label>
                                  <div style={{ width: '100%' }}>
                                    <Select defaultValue={consent.ct_cp_map_bucket[0].frequency}>
                                      <SelectTrigger
                                        style={{
                                          width: '100%',
                                          fontFamily:
                                            config?.consent_purpose_configuration?.form
                                              ?.font_family,
                                        }}
                                      >
                                        <SelectValue placeholder="Select Frequency" />
                                      </SelectTrigger>
                                      <SelectContent>
                                        <SelectGroup>
                                          <SelectLabel>Frequency</SelectLabel>
                                          {frequencyList.map((option) => (
                                            <SelectItem value={option.key} key={option.key}>
                                              {option.name}
                                            </SelectItem>
                                          ))}
                                        </SelectGroup>
                                      </SelectContent>
                                    </Select>
                                  </div>
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      ))}
                    </>
                  )}
                </div>
              </div>
            ))}
            {config?.consent_purpose_configuration?.form_footer?.heading?.text && (
              <p
                style={{
                  marginTop: '20px',
                  width: '100%',
                  fontSize: config?.consent_purpose_configuration?.form_footer?.heading?.size,
                  fontFamily: config?.consent_purpose_configuration?.form?.font_family,
                  color: config?.consent_purpose_configuration?.form_footer?.heading?.color,
                  textAlign: 'justify',
                }}
              >
                {config?.consent_purpose_configuration?.form_footer?.heading?.text}
              </p>
            )}

            {config?.consent_purpose_configuration?.form_footer?.description?.text && (
              <p
                style={{
                  marginTop: '20px',
                  width: '100%',
                  fontSize: config?.consent_purpose_configuration?.form_footer?.description?.size,
                  fontFamily: config?.consent_purpose_configuration?.form?.font_family,
                  color: config?.consent_purpose_configuration?.form_footer?.description?.color,
                  textAlign: 'justify',
                }}
              >
                {config?.consent_purpose_configuration?.form_footer?.description?.text}
              </p>
            )}
            <footer className="flex w-full justify-end">
              <button
                style={{
                  fontFamily: config?.consent_purpose_configuration?.form?.font_family,
                  fontSize: config?.consent_purpose_configuration?.submit_button?.size,
                  background: config?.consent_purpose_configuration?.submit_button?.color,
                  color: 'white',
                  marginTop: '10px',
                  borderRadius: config?.consent_purpose_configuration?.form?.border_radius,
                  padding: '10px 20px 10px 20px',
                  width: 'fit-content',
                  height: 'fit-content',
                  cursor: 'pointer',
                }}
              >
                {config?.consent_purpose_configuration?.submit_button?.text}
              </button>
            </footer>
          </div>
        )}
        {activeTab === '2' && (
          <div className="flex w-full flex-col gap-4">
            <div dangerouslySetInnerHTML={{ __html: config?.dsrContent }}></div>
          </div>
        )}
      </section>
    </main>
  );
};

export default UCMPreferenceForm;
