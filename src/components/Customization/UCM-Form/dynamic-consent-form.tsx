import axios from 'axios';
import { ChangeEvent, Dispatch, SetStateAction, useEffect, useState } from 'react';
import { Badge } from '../../../@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from '../../../@/components/ui/Common/Elements/Select/Select';
import Spinner from '../../../components/common/spinner';
import { FormConfiguration } from '../../../types/universal-consent-management';
import {
  CTCPMapBucket,
  DataType,
  FrequencyProperties,
  PiiValuesType,
} from '../../UniversalConsentMangement/UCMLabs/ConsentBuilder/ConsentBuilderSteps/source-step';
// import { get_frequency } from '../../common/services/universal-consent-management';
export interface Languages {
  language_code: string;
  language: string;
}
interface UCMFormProperties {
  formConfig: FormConfiguration;
  setConsentStatus?: Dispatch<SetStateAction<Map<number, CTCPMapBucket[]>>>;
  consentStatus?: Map<number, CTCPMapBucket[]>;
  formData?: DataType | undefined;
  isLoading?: boolean;
  frequencyList?: FrequencyProperties[];
  setPiiValues?: Dispatch<SetStateAction<PiiValuesType>>;
  piiValues?: PiiValuesType;
  languages?: Languages[];
  setConfig?: React.Dispatch<React.SetStateAction<FormConfiguration>>;
}

const DynamicConsentForm: React.FC<UCMFormProperties> = ({
  formConfig,
  setConsentStatus,
  consentStatus,
  formData,
  isLoading,
  frequencyList,
  setPiiValues,
  piiValues,
  languages,
}) => {
  const [selectedLanguage, setSelectedLanguage] = useState<string>('en');

  const [hasError, setHasError] = useState<boolean>(false);
  const [ip, setIP] = useState('');
  const [countryCode, setCountryCode] = useState<string>('');
  const [continent, setContinent] = useState<string>('');

  const allConsents = consentStatus ? Array.from(consentStatus.values()).flat() : [];
  const allChecked =
    allConsents.length > 0 &&
    allConsents.every((consent) => consent.consent_status || consent.compulsory_consent);

  // fetching ip and country code
  useEffect(() => {
    const getData = async () => {
      const response = await axios.get('https://api.ipify.org/?format=json');
      setIP(response.data.ip);
    };

    const getCountry = async () => {
      const response = await axios.get(
        'https://api.ipdata.co?api-key=295ad47616f5cc86434a4a501a2b9dba7eb6aeec63a70e09620aab10'
      );
      setCountryCode(response?.data?.country_code);
      setContinent(response?.data?.continent_name);
    };

    getData();
    getCountry();
  }, []);

  const handlePiiChange = (id: number, value: string) => {
    if (!setPiiValues) return;
    setPiiValues((prev) => ({ ...prev, [id]: value }));
  };

  const handleConsentChange = (consentId: number, checked: boolean) => {
    if (!setConsentStatus) return;

    setConsentStatus((prev) => {
      // Clone the existing Map (React state requires immutability)
      const updatedMap = new Map(prev);
      let updatedValue: CTCPMapBucket[] | undefined = updatedMap?.get(consentId);

      if (updatedValue) {
        updatedValue = updatedValue.map((item) => ({
          ...item,
          consent_status: checked,
        }));

        updatedMap.set(consentId, updatedValue);
      }
      // Update existing entry OR add a new one

      return updatedMap; // Update state with new Map
    });
  };

  const handleConsentChangeAll = (checked: boolean) => {
    if (!setConsentStatus) return;

    setConsentStatus((prev) => {
      const updatedMap = new Map<number, CTCPMapBucket[]>();
      // For every consent purpose (key) in your Map, update the consent_status for each bucket entry.
      prev.forEach((consentList, consentId) => {
        const updatedConsentList = consentList.map((item) => {
          if (!item.compulsory_consent) {
            return {
              ...item,
              consent_status: checked,
            };
          } else {
            return item;
          }
        });
        updatedMap.set(consentId, updatedConsentList);
      });
      return updatedMap;
    });
  };

  // fetching frequency

  if (isLoading) {
    return (
      <div
        style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100%',
          width: '100%',
          fontSize: '14px',
          color: '#000',
          fontWeight: 'bold',
          textAlign: 'center',
        }}
      >
        <Spinner />
      </div>
    );
  }

  if (hasError) {
    return (
      <div
        style={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          height: '100%',
          width: '100%',
          textAlign: 'center',
        }}
      >
        <p style={{ color: '#FF0000', fontSize: '14px', fontWeight: 'bold' }}>
          An error occurred while loading the form.
        </p>
        <button onClick={() => window.location.reload()}>Retry</button>
      </div>
    );
  }

  return (
    <main style={{ height: '100%', width: '100%' }}>
      <main
        style={{
          height: '100%',
          width: '100%',
          fontFamily: formConfig?.form?.font_family,
        }}
      >
        <section
          style={{
            display: 'flex',
            height: '100%',
            width: '100%',
            justifyContent: 'center',
          }}
        >
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '8px',
              borderRadius: '8px',
              border: '1px solid #ccc',
              overflowY: 'auto',
              maxWidth: '90%',
              width: '100%',
              boxSizing: 'border-box',
            }}
          >
            <header
              style={{
                display: 'flex',
                flexDirection: 'row',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '8px',
                gap: '8px',
                width: '100%',
                height: 'fit-content',
              }}
            >
              {formConfig?.form?.logo?.show_logo && (
                <img
                  src={formConfig?.form?.logo?.logo_url}
                  alt="Logo"
                  style={{
                    height: formConfig?.form?.logo?.height,
                    width: formConfig?.form?.logo?.width,
                    objectFit: 'contain',
                  }}
                />
              )}
              <h2
                style={{
                  fontSize: formConfig?.form?.heading?.size,
                  fontFamily: formConfig?.form?.font_family,
                  color: formConfig?.form?.heading?.color,
                  fontWeight: 'bold',
                }}
              >
                {formConfig?.form?.heading?.text}
              </h2>
              <div
                style={{
                  display: 'flex',
                  width: 'fit-content',
                  flexDirection: 'row',
                  alignItems: 'flex-start',
                  gap: '8px' /* gap-2 = 8px */,
                }}
              >
                {formConfig?.form?.show_language && (
                  <select
                    value={selectedLanguage}
                    onChange={(event: React.ChangeEvent<HTMLSelectElement>) =>
                      setSelectedLanguage(event.target.value)
                    }
                    style={{
                      height: '44px',
                      width: '124px',
                      border: '1px solid #d1d1d1',
                      backgroundColor: '#f3f4f6' /* bg-gray-100 */,
                      paddingLeft: '8px' /* px-2 = 8px */,
                      paddingRight: '8px',
                    }}
                  >
                    <option value="en" key="en">
                      English
                    </option>
                    {languages?.map((lang: { language_code: string; language: string }) => (
                      <option value={lang.language_code} key={lang.language_code}>
                        {lang.language}
                      </option>
                    ))}
                  </select>
                )}
                <button>✕</button>
              </div>
            </header>

            <hr
              style={{
                height: '0.5px',
                backgroundColor: '#ccc',
                marginLeft: '20px',
                marginRight: '20px',
              }}
            />

            <section
              style={{
                paddingLeft: '16px',
                paddingRight: '16px',
              }}
            >
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', width: '100%' }}>
                <p
                  style={{
                    width: '100%',
                    textAlign: 'justify',
                    fontSize: formConfig?.form?.description?.size,
                    fontFamily: formConfig?.form?.font_family,
                    color: formConfig?.form?.description?.color,
                  }}
                >
                  {formConfig?.form?.description?.text}
                </p>
              </div>

              {/* <hr
                style={{
                  height: '0.5px',
                  backgroundColor: '#ccc',
                  margin: '10px',
                }}
              /> */}

              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', width: '100%' }}>
                {formConfig?.pii_section?.heading?.text && (
                  <p
                    style={{
                      fontWeight: 'bold',
                      fontSize: formConfig?.pii_section?.heading?.size,
                      fontFamily: formConfig?.form?.font_family,
                      color: formConfig?.pii_section?.heading?.color,
                    }}
                  >
                    {formConfig?.pii_section?.heading?.text}
                  </p>
                )}
                {formConfig?.pii_section?.description?.text && (
                  <p
                    style={{
                      fontSize: formConfig?.pii_section?.description?.size,
                      fontFamily: formConfig?.form?.font_family,
                      color: formConfig?.pii_section?.description?.color,
                      textAlign: 'justify',
                    }}
                  >
                    {formConfig?.pii_section?.description?.text}
                  </p>
                )}
                {formConfig?.pii_section?.show_pii_section && (
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: '8px',
                      width: '100%',
                    }}
                  >
                    {formData?.pii_list.map((piiLabel) => (
                      <div
                        key={piiLabel.pii_label_id}
                        style={{
                          display: 'flex',
                          flexDirection: 'row',
                          flexWrap: 'wrap',
                          alignItems: 'center',
                          gap: '16px',
                        }}
                      >
                        {formConfig?.pii_section?.show_labels && (
                          <label
                            style={{
                              width: '200px',
                              fontWeight: 'normal',
                            }}
                          >
                            {piiLabel.pii_label_name}:{' '}
                          </label>
                        )}
                        <input
                          placeholder={`Enter ${piiLabel.pii_label_name}`}
                          style={{
                            border: '1px solid #ccc',
                            borderRadius: formConfig?.form?.border_radius,
                            padding: '8px',
                            height: '40px',
                            width: '200px',
                            fontWeight: 'normal',
                            fontFamily: formConfig?.form?.font_family,
                          }}
                          value={piiValues?.[piiLabel.pii_label_id]}
                          onChange={(e) =>
                            handlePiiChange(
                              piiLabel.pii_label_id,
                              (e.target as HTMLInputElement).value
                            )
                          }
                          required
                        />
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* <hr
                style={{
                  height: '0.5px',
                  backgroundColor: '#ccc',
                  margin: '10px',
                }}
              /> */}

              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', width: '100%' }}>
                <p
                  style={{
                    fontWeight: 'bold',
                    fontSize: formConfig?.consent_collection_section?.heading?.size,
                    fontFamily: formConfig?.form?.font_family,
                    color: formConfig?.consent_collection_section?.heading?.color,
                  }}
                >
                  {formConfig?.consent_collection_section?.heading?.text}
                </p>
                <p
                  style={{
                    fontSize: formConfig?.consent_collection_section?.description?.size,
                    fontFamily: formConfig?.form?.font_family,
                    color: formConfig?.consent_collection_section?.description?.color,
                  }}
                >
                  {formConfig?.consent_collection_section?.description?.text}
                </p>
                {formConfig?.consent_collection_section?.show_check_all_checkbox ? (
                  <div>
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'row',
                        alignItems: 'start',
                        gap: '8px',
                      }}
                    >
                      <input
                        type="checkbox"
                        style={{
                          marginTop: '4px',
                          height: '18px',
                          width: '18px',
                          cursor: 'pointer',
                          accentColor: formConfig?.form?.color_scheme,
                          borderRadius: formConfig?.form?.border_radius,
                        }}
                        checked={allChecked}
                        onChange={(event: ChangeEvent<HTMLInputElement>) =>
                          handleConsentChangeAll(event.target.checked)
                        }
                      />
                      <p
                        style={{
                          fontFamily: formConfig?.form?.font_family,
                          fontSize: formConfig?.consent_collection_section?.description?.size,
                        }}
                      >
                        {formConfig?.consent_collection_section?.all_checkbox_text}
                      </p>
                    </div>
                    <ul style={{ listStyleType: 'disc', paddingLeft: '2.5rem' }}>
                      {formData?.purpose_list?.map((purpose) => (
                        <>
                          {/* Nested list for consent buckets */}
                          {purpose.consent_bucket.map((consent) => (
                            <li key={consent.consent_purpose_id} style={{ marginBottom: '1rem' }}>
                              {/* Wrap your flex layout in a child div */}
                              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                                <p style={{ margin: 0 }}>
                                  <strong>{consent.consent_purpose_name}</strong>
                                </p>
                                <p style={{ margin: 0 }}>{consent.consent_purpose_description}</p>
                                {/* PII Labels */}
                                {consent?.pii_labels?.length > 0 &&
                                  formConfig?.pii_section?.show_badges && (
                                    <div
                                      style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        flexWrap: 'wrap',
                                        gap: '8px',
                                      }}
                                    >
                                      <span>PII:</span>
                                      {consent.pii_labels.map((label, index) =>
                                        label ? (
                                          <Badge
                                            key={index}
                                            style={{
                                              color: 'white',
                                              backgroundColor: formConfig?.form?.color_scheme,
                                            }}
                                          >
                                            {label}
                                          </Badge>
                                        ) : null
                                      )}
                                    </div>
                                  )}
                                {/* Vendors */}
                                {consent?.vendors?.length > 0 &&
                                  formConfig?.pii_section?.show_badges && (
                                    <div
                                      style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        flexWrap: 'wrap',
                                        gap: '8px',
                                      }}
                                    >
                                      <span>Vendor:</span>
                                      {consent.vendors.map((vendor, index) =>
                                        vendor ? (
                                          <Badge
                                            key={index}
                                            style={{
                                              color: 'white',
                                              backgroundColor: formConfig?.form?.color_scheme,
                                            }}
                                          >
                                            {vendor}
                                          </Badge>
                                        ) : null
                                      )}
                                    </div>
                                  )}
                                {/* Processes */}
                                {consent?.processes?.length > 0 &&
                                  formConfig?.pii_section?.show_badges && (
                                    <div
                                      style={{
                                        display: 'flex',
                                        flexDirection: 'row',
                                        flexWrap: 'wrap',
                                        gap: '8px',
                                      }}
                                    >
                                      <span>Process:</span>
                                      {consent.processes.map((process, index) =>
                                        process ? (
                                          <Badge
                                            key={index}
                                            style={{
                                              color: 'white',
                                              backgroundColor: formConfig?.form?.color_scheme,
                                            }}
                                          >
                                            {process}
                                          </Badge>
                                        ) : null
                                      )}
                                    </div>
                                  )}
                              </div>
                            </li>
                          ))}
                        </>
                      ))}
                    </ul>
                  </div>
                ) : (
                  <>
                    {formData?.purpose_list.map((purpose) => (
                      <div key={purpose.processing_purpose_id}>
                        {' '}
                        {purpose.consent_bucket.map((consent) => (
                          <div
                            key={consent.consent_purpose_id}
                            style={{
                              marginTop: '8px',
                              display: 'grid',
                              width: '100%',
                              alignItems: 'start',
                              gap: '16px',
                              gridTemplateColumns: consent.ct_cp_map_bucket[0]?.frequency
                                ? '4fr 1fr'
                                : '1fr',
                            }}
                          >
                            <div
                              style={{
                                display: 'flex',
                                flexDirection: 'row',
                                width: '100%',
                                alignItems: 'flex-start',
                                gap: '8px',
                              }}
                            >
                              <div
                                style={{
                                  display: 'flex',
                                  alignItems: 'center',
                                }}
                              >
                                <input
                                  style={{
                                    height: '16px',
                                    width: '16px',
                                    marginTop: '2px',
                                    cursor:
                                      consent.ct_cp_map_bucket[0]?.default_opt_out_allowed &&
                                      consent.ct_cp_map_bucket[0]?.compulsory_consent
                                        ? 'not-allowed'
                                        : 'pointer',
                                    accentColor: formConfig?.form?.color_scheme,
                                    borderRadius: formConfig?.form?.border_radius,
                                  }}
                                  defaultChecked={
                                    consentStatus?.get(consent?.consent_purpose_id)?.[0]
                                      ?.consent_status
                                      ? true
                                      : consent.ct_cp_map_bucket[0]?.default_opt_out_allowed
                                        ? true
                                        : false
                                  }
                                  type="checkbox"
                                  checked={
                                    consentStatus?.get(consent?.consent_purpose_id)?.[0]
                                      ?.consent_status
                                  }
                                  onChange={(event: ChangeEvent<HTMLInputElement>) =>
                                    handleConsentChange(
                                      consent.consent_purpose_id,
                                      event.target.checked
                                    )
                                  }
                                  required={consent.ct_cp_map_bucket[0]?.compulsory_consent}
                                  disabled={
                                    consent.ct_cp_map_bucket[0]?.default_opt_out_allowed &&
                                    consent.ct_cp_map_bucket[0]?.compulsory_consent
                                  }
                                  id={consent?.consent_purpose_id?.toString()}
                                />
                                <label
                                  htmlFor={consent?.consent_purpose_id?.toString()}
                                  className="text-sm text-gray-700"
                                >
                                  {consent?.ct_cp_map_bucket[0]?.compulsory_consent && (
                                    <span className="ml-1 text-red-500">*</span>
                                  )}
                                </label>
                              </div>

                              <div className="flex flex-col flex-wrap gap-2">
                                <p className="flex flex-col gap-2">
                                  <strong>{consent.consent_purpose_name}</strong>
                                  <p>{consent.consent_purpose_description}</p>
                                </p>
                                {/* PII Labels */}
                                {consent?.pii_labels?.length > 0 &&
                                  formConfig?.pii_section?.show_badges && (
                                    <p className="flex w-full flex-row flex-wrap gap-2">
                                      <p>PII:</p>
                                      {consent?.pii_labels?.map((consent) => {
                                        if (consent) {
                                          return (
                                            <Badge
                                              style={{
                                                color: 'white',
                                                backgroundColor: formConfig?.form?.color_scheme,
                                              }}
                                            >
                                              {consent}
                                            </Badge>
                                          );
                                        }
                                        return <></>;
                                      })}
                                    </p>
                                  )}
                                {/* Vendors */}
                                {consent?.vendors?.length > 0 &&
                                  formConfig?.pii_section?.show_badges && (
                                    <p className="flex w-full flex-row flex-wrap gap-2">
                                      <p>Vendor:</p>
                                      {consent?.vendors?.map((consent) => {
                                        if (consent) {
                                          return (
                                            <Badge
                                              style={{
                                                color: 'white',
                                                backgroundColor: formConfig?.form?.color_scheme,
                                              }}
                                            >
                                              {consent}
                                            </Badge>
                                          );
                                        }
                                        return <></>;
                                      })}
                                    </p>
                                  )}
                                {/* Processes */}
                                {consent?.processes?.length > 0 &&
                                  formConfig?.pii_section?.show_badges && (
                                    <p className="flex w-full flex-row flex-wrap gap-2">
                                      <p>Process:</p>
                                      {consent?.processes?.map((consent) => {
                                        if (consent) {
                                          return (
                                            <Badge
                                              style={{
                                                color: 'white',
                                                backgroundColor: formConfig?.form?.color_scheme,
                                              }}
                                            >
                                              {consent}
                                            </Badge>
                                          );
                                        }
                                        return <></>;
                                      })}
                                    </p>
                                  )}
                              </div>
                            </div>

                            {consent.ct_cp_map_bucket[0]?.frequency && (
                              <div
                                style={{
                                  display: 'flex',
                                  flexDirection: 'row',
                                  alignItems: 'center',
                                  gap: '8px',
                                  width: '100%',
                                  paddingRight: '10px',
                                }}
                              >
                                <label
                                  style={{
                                    width: 'auto',
                                  }}
                                >
                                  Frequency:
                                </label>
                                <div style={{ width: '100%' }}>
                                  <Select defaultValue={consent.ct_cp_map_bucket[0].frequency}>
                                    <SelectTrigger
                                      style={{
                                        width: '100%',
                                        fontFamily: formConfig?.form?.font_family,
                                      }}
                                    >
                                      <SelectValue placeholder="Select Frequency" />
                                    </SelectTrigger>
                                    <SelectContent>
                                      <SelectGroup>
                                        <SelectLabel>Frequency</SelectLabel>
                                        {frequencyList?.map((option) => (
                                          <SelectItem value={option.key} key={option.key}>
                                            {option.name}
                                          </SelectItem>
                                        ))}
                                      </SelectGroup>
                                    </SelectContent>
                                  </Select>
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    ))}
                  </>
                )}
              </div>

              {formConfig?.form_footer?.heading?.text && (
                <p
                  style={{
                    marginTop: '20px',
                    width: '100%',
                    fontSize: formConfig?.form_footer?.heading?.size,
                    fontFamily: formConfig?.form?.font_family,
                    color: formConfig?.form_footer?.heading?.color,
                    textAlign: 'justify',
                  }}
                >
                  {formConfig?.form_footer?.heading?.text}
                </p>
              )}

              {formConfig?.form_footer?.description?.text && (
                <p
                  style={{
                    marginTop: '20px',
                    width: '100%',
                    fontSize: formConfig?.form_footer?.description?.size,
                    fontFamily: formConfig?.form?.font_family,
                    color: formConfig?.form_footer?.description?.color,
                    textAlign: 'justify',
                  }}
                >
                  <div
                    dangerouslySetInnerHTML={{ __html: formConfig?.form_footer?.description?.text }}
                  ></div>
                </p>
              )}
              <div
                style={{
                  width: '100%',
                  display: 'flex',
                  flexDirection: 'row',
                  justifyContent: 'flex-end',
                  alignItems: 'center',
                  gap: '10px',
                  marginBottom: '20px',
                }}
              >
                {formConfig?.form?.show_privacy_policy_url && (
                  <a
                    href={
                      formConfig?.form?.privacy_policy_url?.startsWith('http')
                        ? formConfig?.form?.privacy_policy_url
                        : `https://${formConfig?.form?.privacy_policy_url}`
                    }
                    style={{
                      fontSize: '14px',
                      fontFamily: formConfig?.form?.font_family,
                      color: formConfig?.form?.color_scheme,
                      textDecoration: 'underline',
                      marginRight: 'auto',
                      marginTop: '8px',
                    }}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    {formConfig?.form?.privacy_policy_url_text}
                  </a>
                )}
                <button
                  style={{
                    fontFamily: formConfig?.form?.font_family,
                    fontSize: formConfig?.submit_button?.size,
                    background: formConfig?.submit_button?.color,
                    color: 'white',
                    marginTop: '10px',
                    borderRadius: formConfig?.form?.border_radius,
                    padding: '10px 20px',
                    width: 'fit-content',
                    height: 'fit-content',
                  }}
                  disabled
                >
                  {formConfig?.submit_button?.text}
                </button>
              </div>
            </section>
          </div>
        </section>
      </main>
    </main>
  );
};

export default DynamicConsentForm;
