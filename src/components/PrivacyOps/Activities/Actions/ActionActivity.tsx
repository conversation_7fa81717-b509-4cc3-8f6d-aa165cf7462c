import { ColumnDef } from '@tanstack/react-table';
import DynamicTable from '../../../common/ShadcnDynamicTable/dynamic-table';

import {
  Card,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '../../../../@/components/ui/card';
import { Button } from '../../../../@/components/ui/Common/Elements/Button/Button';

import { Input } from '../../../../@/components/ui/Common/Elements/Input/Input';

import { Action } from '@radix-ui/react-alert-dialog';
import toast from 'react-hot-toast';
import { useTranslation } from 'react-i18next';
import { z } from 'zod';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../../@/components/ui/Common/Elements/Select/Select';
import httpClient from '../../../../api/httpClient';
import add from '../../../../assets/plusSign.svg';
import search from '../../../../assets/Search.svg';
import { useActivityHook } from '../../../../hooks/Privacy-ops/useActivityHook';
import { ADD_ACTION } from '../../../common/api';
import AvatarFrame from '../../../common/Avatar';
import { convertDateToHumanView } from '../../../common/CommonHelperFunctions';
import { DynamicColumnHeader } from '../../../common/ShadcnDynamicTable/DynamicColumnHeader';
import { AddAction } from './AddAction';
import { AddActivitySchema } from './AddActivitySchema';
interface Assigned {
  id: number;
  name: string;
  initial: string;
}
export interface Action {
  id: string;
  title: string;
  AssignedTo: Assigned;
  AssignedBy: Assigned;
  assigned_date: string;
  deadline_date: string;
  status: string;
}
export interface SubmittedDataProperties {
  title: string;
  description: string;
  assigned_by: number;
  assigned_to: number;
  assigned_date: string;
  deadline_date: string;
  regulation_id?: number | null;
  entity_id: number | null;
  custom_busi_requirement_id?: number | null;

  data_breach_id?: number | null;
  action_type?: string;
}
export interface UserData {
  id: number;
  firstName: string;
}
export interface ActionCount {
  actions: string;
  count: string;
}

export default function ActionActivity() {
  const { t } = useTranslation();
  const columns: ColumnDef<Action>[] = [
    {
      accessorKey: 'title',
      header: ({ column }) => (
        <DynamicColumnHeader
          column={column}
          headerName={t('DPO.Activities.Action.ActionActivity.ActionTitle')}
        />
      ),
      cell: (info) => {
        const title = info.getValue() as string;
        const name: string = title || '-';

        return <div className="">{name}</div>;
      },
    },
    {
      accessorKey: 'AssignedTo',
      header: ({ column }) => (
        <DynamicColumnHeader
          column={column}
          headerName={t('DPO.Activities.Action.ActionActivity.AssignedTo')}
        />
      ),
      cell: ({ row }) => {
        const assignedTo = row.original.AssignedTo;
        return (
          <div className="flex items-center gap-2">
            <span>
              <AvatarFrame
                value={assignedTo?.name}
                getInitials={(name) => assignedTo?.initial || name.charAt(0).toUpperCase()}
              />
            </span>
            <span>{assignedTo?.name || '-'}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'AssignedBy',
      header: ({ column }) => (
        <DynamicColumnHeader
          column={column}
          headerName={t('DPO.Activities.Action.ActionActivity.AssignedBy')}
        />
      ),
      cell: ({ row }) => {
        const assignedBy = row.original.AssignedBy;
        return (
          <div className="flex items-center gap-2">
            <span>
              <AvatarFrame
                value={assignedBy?.name}
                getInitials={(name) => assignedBy?.initial || name.charAt(0).toUpperCase()}
              />
            </span>
            <span>{assignedBy?.name || '-'}</span>
          </div>
        );
      },
    },
    {
      accessorKey: 'assigned_date',
      header: ({ column }) => (
        <DynamicColumnHeader
          column={column}
          headerName={t('DPO.Activities.Action.ActionActivity.AssignedDate')}
        />
      ),
      cell: (info) => {
        const createdAt = info.getValue() as string;
        const formattedCreatedAt = convertDateToHumanView(createdAt);
        return <div className="">{createdAt ? formattedCreatedAt : '-'}</div>;
      },
    },
    {
      accessorKey: 'deadline_date',
      header: ({ column }) => (
        <DynamicColumnHeader
          column={column}
          headerName={t('DPO.Activities.Action.ActionActivity.Deadline')}
        />
      ),
      cell: (info) => {
        const createdAt = info.getValue() as string;
        const formattedCreatedAt = convertDateToHumanView(createdAt);
        return <div className="">{createdAt ? formattedCreatedAt : '-'}</div>;
      },
    },
    {
      accessorKey: 'status',
      header: ({ column }) => (
        <DynamicColumnHeader
          column={column}
          headerName={t('DPO.Activities.Action.ActionActivity.Status')}
        />
      ),
      cell: (info) => {
        const status = info.getValue() as string;

        const name: string = status || '-';
        return (
          <div className="flex items-center justify-center gap-2 rounded-md border border-[#FF4746] bg-[#FFDEDE] p-1 font-medium">
            {' '}
            {name}
          </div>
        );
      },
    },
  ];
  const {
    actionData,
    getAction,
    isActionLoading,
    userData,
    actionCount,
    getActionsCount,
    settingShowDialog,
    showDialog,
    updateSearchTerm,
    entities,
    selectedEntityId,
    setSelectedEntityId,
  } = useActivityHook();

  const handleSubmit = async (submittedData: z.infer<typeof AddActivitySchema>) => {
    // const { t } = useTranslation();
    const finalData: SubmittedDataProperties = {
      description: submittedData?.description,
      assigned_by: Number(submittedData?.assigned_by),
      assigned_to: Number(submittedData?.assigned_to),
      title: submittedData?.title,
      assigned_date: submittedData?.assigned_date,
      deadline_date: submittedData?.deadline_date,
      regulation_id: Number(submittedData?.regulation_id),
      entity_id: Number(submittedData?.entity_id),
      custom_busi_requirement_id: Number(submittedData?.custom_busi_requirement_id),
    };
    const response = await httpClient.post(ADD_ACTION, finalData);
    if (response?.status === 200) {
      toast.dismiss();
      toast.success(t('DPO.Activities.Action.ActionActivity.ActionAddedSuccessfully'));
      getAction();
      settingShowDialog(false);
      getActionsCount();
    }
  };

  return (
    <div>
      {showDialog && (
        <AddAction
          showDialog={showDialog}
          userData={userData}
          entities={entities}
          handleSubmit={handleSubmit}
          setShowDialog={settingShowDialog}
        />
      )}
      <div className="flex items-center justify-end gap-3">
        <div className="relative inline-block">
          <Input
            type="text"
            onChange={(event) => updateSearchTerm(event)}
            placeholder={t('Common.Search')}
            className="pl-10 pr-3"
          />
          <img
            src={search}
            alt={t('Common.Search')}
            className="pointer-events-none absolute inset-y-0 left-3 top-1/2 h-5 w-5 -translate-y-1/2 transform"
          />
        </div>

        <div className="w-[192px]">
          <Select value={selectedEntityId} onValueChange={(value) => setSelectedEntityId(value)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select option" />
            </SelectTrigger>
            <SelectContent>
              {entities?.map((entity) => (
                <SelectItem key={entity?.id} value={entity?.id?.toString()}>
                  {entity?.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Button
          className="theme-hover-effect h-12 bg-primary px-4 text-sm text-white"
          onClick={() => settingShowDialog(true)}
        >
          <img src={add} className="h-[20px] w-[20px] text-white" />
          <span className="mx-2">{t('DPO.Activities.Action.ActionActivity.AddNew')}</span>
        </Button>
      </div>
      <div className="py-4">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <Card className="rounded-lg border-[1px] border-[#C0CDE0]">
            <CardHeader>
              <CardDescription>
                {t('DPO.Activities.Action.ActionActivity.TotalActionItems')}
              </CardDescription>
            </CardHeader>
            <CardFooter>
              <CardTitle>{actionCount[0]?.count} </CardTitle>
            </CardFooter>
          </Card>
          <Card className="rounded-lg border-[1px] border-[#C0CDE0]">
            <CardHeader>
              <CardDescription>
                {t('DPO.Activities.Action.ActionActivity.OpenActionItems')}
              </CardDescription>
            </CardHeader>
            <CardFooter>
              <CardTitle>{actionCount[1]?.count}</CardTitle>
            </CardFooter>
          </Card>
          <Card className="rounded-lg border-[1px] border-[#C0CDE0]">
            <CardHeader>
              <CardDescription>
                {t('DPO.Activities.Action.ActionActivity.ClosedActionItems')}
              </CardDescription>
            </CardHeader>
            <CardFooter>
              <CardTitle>{actionCount[2]?.count} </CardTitle>
            </CardFooter>
          </Card>
        </div>
        <div
          style={{ height: 'calc(100vh - 20rem)' }}
          className="table_main_content mt-0 w-full overflow-auto"
        >
          <DynamicTable<Action>
            data={actionData}
            loading={isActionLoading}
            columns={columns}
            enableSorting
            enablePagination
          />
        </div>
      </div>
    </div>
  );
}
