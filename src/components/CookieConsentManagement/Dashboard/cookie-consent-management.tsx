import { Clock, Edit, Globe, Plus, Shield, Trash2, User } from 'lucide-react';
import { Badge } from '../../../@/components/ui/badge';
import { Button } from '../../../@/components/ui/Common/Elements/Button/Button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../@/components/ui/Common/Elements/Card/Card';
import { Input } from '../../../@/components/ui/Common/Elements/Input/Input';
import { Label } from '../../../@/components/ui/Common/Elements/Label/Label';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../@/components/ui/Common/Table/Table';
import { Switch } from '../../../@/components/ui/switch';

export function ConsentManagement() {
  const consentCategories = [
    {
      id: 1,
      name: 'Strictly Necessary',
      description: 'Essential for website operation and security',
      cookieCount: 12,
      defaultOptOut: false,
      required: true,
      legalBasis: 'Legitimate Interest',
    },
    {
      id: 2,
      name: 'Performance & Analytics',
      description: 'Help us understand how visitors use our website',
      cookieCount: 8,
      defaultOptOut: true,
      required: false,
      legalBasis: 'Consent',
    },
    {
      id: 3,
      name: 'Functional',
      description: 'Remember choices and provide enhanced features',
      cookieCount: 5,
      defaultOptOut: true,
      required: false,
      legalBasis: 'Consent',
    },
    {
      id: 4,
      name: 'Marketing & Advertising',
      description: 'Used to deliver relevant advertisements',
      cookieCount: 15,
      defaultOptOut: true,
      required: false,
      legalBasis: 'Consent',
    },
  ];

  const consentRecords = [
    {
      id: 'CR001',
      userId: 'user123',
      timestamp: '2024-01-15 14:30:22',
      location: 'Germany',
      device: 'Desktop',
      method: 'Accept All',
      categories: ['necessary', 'analytics', 'marketing'],
      status: 'Active',
    },
    {
      id: 'CR002',
      userId: 'user456',
      timestamp: '2024-01-15 13:45:10',
      location: 'California, US',
      device: 'Mobile',
      method: 'Custom Selection',
      categories: ['necessary', 'functional'],
      status: 'Active',
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Consent Management</h2>
          <p className="text-slate-600">Configure consent categories and track user preferences</p>
        </div>
        <Button className="bg-custom-primary text-white hover:bg-custom-primary hover:text-white">
          <Plus className="mr-2 h-4 w-4" />
          Add Category
        </Button>
      </div>

      {/* Consent Categories */}
      <Card>
        <CardHeader>
          <CardTitle>Consent Categories</CardTitle>
          <CardDescription>Manage cookie categories and their purposes</CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Category</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Cookies</TableHead>
                <TableHead>Legal Basis</TableHead>
                <TableHead>Default Opt-Out</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {consentCategories.map((category) => (
                <TableRow key={category.id}>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Shield className="h-4 w-4 text-blue-600" />
                      <div>
                        <div className="font-medium">{category.name}</div>
                        {category.required && (
                          <Badge variant="secondary" className="text-xs">
                            Required
                          </Badge>
                        )}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell className="max-w-xs text-slate-600">{category.description}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{category.cookieCount} cookies</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={category.legalBasis === 'Consent' ? 'default' : 'secondary'}>
                      {category.legalBasis}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Switch checked={category.defaultOptOut} disabled={category.required} />
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-1">
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm" disabled={category.required}>
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Consent Records */}
      <Card>
        <CardHeader>
          <CardTitle>Consent Records</CardTitle>
          <CardDescription>Track and audit user consent decisions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-4">
            <Input placeholder="Search by user ID, location, or device..." className="max-w-md" />
          </div>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Record ID</TableHead>
                <TableHead>User</TableHead>
                <TableHead>Timestamp</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Device</TableHead>
                <TableHead>Method</TableHead>
                <TableHead>Categories</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {consentRecords.map((record) => (
                <TableRow key={record.id}>
                  <TableCell className="font-mono text-sm">{record.id}</TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4 text-slate-400" />
                      <span className="font-mono text-sm">{record.userId}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Clock className="h-4 w-4 text-slate-400" />
                      <span className="text-sm">{record.timestamp}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Globe className="h-4 w-4 text-slate-400" />
                      <span className="text-sm">{record.location}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{record.device}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={record.method === 'Accept All' ? 'default' : 'secondary'}>
                      {record.method}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex flex-wrap gap-1">
                      {record.categories.map((cat) => (
                        <Badge key={cat} variant="outline" className="text-xs">
                          {cat}
                        </Badge>
                      ))}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={record.status === 'Active' ? 'default' : 'secondary'}>
                      {record.status}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Lifecycle Management */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Consent Expiration</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>Default Expiration Period</Label>
                <div className="flex space-x-2">
                  <Input type="number" placeholder="12" className="w-20" />
                  <Badge variant="outline">months</Badge>
                </div>
              </div>
              <div className="text-sm text-slate-600">
                Automatically expire consent after specified period
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Withdrawal Options</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch id="allowWithdrawal" />
                <Label htmlFor="allowWithdrawal">Allow easy withdrawal</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="showPreferences" />
                <Label htmlFor="showPreferences">Show preference center</Label>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Audit Trail</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="text-2xl font-bold text-green-600">98.5%</div>
              <div className="text-sm text-slate-600">Compliance score</div>
              <Button variant="outline" size="sm" className="w-full">
                View Audit Report
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
