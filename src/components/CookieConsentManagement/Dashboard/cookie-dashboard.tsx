import { useQuery } from '@tanstack/react-query';
import {
  <PERSON><PERSON>hart<PERSON>,
  CheckCircle,
  Cookie,
  Download,
  Eye,
  Globe,
  MousePointer,
  Scan,
  Settings,
  Shield,
} from 'lucide-react';
import { useState } from 'react';
import { useSelector } from 'react-redux';
import {
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';
import { Button } from '../../../@/components/ui/Common/Elements/Button/Button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../@/components/ui/Common/Elements/Card/Card';
import { Progress } from '../../../@/components/ui/Common/Elements/ProgressBar/ProgressBar';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '../../../@/components/ui/Common/Elements/Tabs/Tabs';
import { Badge } from '../../../@/components/ui/badge';
import httpClient from '../../../api/httpClientNew';
import { RootState } from '../../../redux/store';
import DonutWithText from '../../common/Charts/donut-with-text';
import { generateDonutChartColors } from '../../common/CommonHelperFunctions';
import { FETCH_COOKIES_BY_CATEGORY } from '../../common/api';

export function CookieDashboard() {
  const customer_id: number | undefined = useSelector(
    (state: RootState) => state?.auth?.login?.login_details?.customer_id
  );

  const [selectedDomainId, setSelectedDomainId] = useState<number>(0);

  const { data: cookieCategoryData } = useQuery({
    queryKey: ['cookie_category_data', customer_id, selectedDomainId],
    queryFn: async () => {
      const response = await httpClient.get(
        `${FETCH_COOKIES_BY_CATEGORY}?customer_id=${customer_id}&domain_id=${selectedDomainId}`
      );
      return response?.data?.data?.result;
    },
    enabled: !!customer_id && selectedDomainId !== null,
  });

  function getUniqueCookieCategories(cookies: any) {
    const uniqueCategories = new Set();
    cookies?.forEach((cookie: any) => {
      if (cookie.category) {
        uniqueCategories.add(cookie.category);
      }
    });

    return Array.from(uniqueCategories);
  }

  const colors = generateDonutChartColors(cookieCategoryData?.length);

  const cookieCategoryDataVales = cookieCategoryData?.map((item: any, index: number) => {
    return {
      key: item?.category_name,
      value: item?.cookie_count,
      fill: colors[index],
    };
  });

  const cookieCategoryDataToUse = {
    config: {
      advertising: {
        label: 'Advertising',
        color: 'hsl(var(--chart-green))',
      },
      analytics: {
        label: "Analytics",
        color: 'hsl(var(--chart-yellow))',
      },
      essential: {
        label: "Essential",
        color: 'hsl(var(--chart-green))',
      },
      functional: {
        label: "Functional",
        color: 'hsl(var(--chart-yellow))',
      },
      performance: {
        label: "Performance",
        color: 'hsl(var(--chart-green))',
      },
      unclassified: {
        label: "Unclassified",
        color: 'hsl(var(--chart-yellow))',
      },
    },
    data: cookieCategoryDataVales ?? [],
  };


  console.log(cookieCategoryDataToUse,"cookieCategoryDataToUse123")

    // Key metrics data
  const keyMetrics = [
    {
      title: 'Total Cookies',
      value: '847',
      subtitle: 'Across all domains',
      icon: Cookie,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      iconColor: 'text-blue-500',
    },
    {
      title: 'Third-Party',
      value: '567',
      subtitle: 'External cookies',
      icon: Globe,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      iconColor: 'text-orange-500',
    },
    {
      title: 'Consent Rate',
      value: '78.5%',
      subtitle: '',
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      iconColor: 'text-green-500',
      showProgress: true,
      progressValue: 78.5,
    },
    {
      title: 'Compliance Score',
      value: '92.3%',
      subtitle: '',
      icon: Shield,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      iconColor: 'text-purple-500',
      showProgress: true,
      progressValue: 92.3,
    },
  ];

  // Cookie category data for pie chart
  const cookieCategories = [
    { name: 'Marketing', value: 234, color: '#ef4444' },
    { name: 'Advertising', value: 156, color: '#8b5cf6' },
    { name: 'Analytics', value: 123, color: '#f59e0b' },
    { name: 'Functional', value: 89, color: '#3b82f6' },
    { name: 'Essential', value: 45, color: '#10b981' },
  ];

  // Consent interaction trends data
  const consentTrends = [
    { month: '2024-01', accepted: 2250, declined: 450 },
    { month: '2024-02', accepted: 2400, declined: 420 },
    { month: '2024-03', accepted: 2550, declined: 380 },
    { month: '2024-04', accepted: 2700, declined: 350 },
    { month: '2024-05', accepted: 2850, declined: 320 },
    { month: '2024-06', accepted: 3000, declined: 300 },
  ];

  // Additional metrics
  const additionalMetrics = [
    { label: 'Active Domains', value: '24', trend: '+3 this month' },
    { label: 'Avg Response Time', value: '1.2s', trend: '-0.3s faster' },
    { label: 'Banner Views', value: '125K', trend: '+12% vs last month' },
    { label: 'Preference Updates', value: '2.3K', trend: '+8% this week' },
  ];

  // Compliance metrics
  const complianceMetrics = [
    { framework: 'GDPR', score: 94, status: 'Compliant' },
    { framework: 'CCPA', score: 89, status: 'Compliant' },
    { framework: 'LGPD', score: 86, status: 'Review Needed' },
    { framework: 'PIPEDA', score: 92, status: 'Compliant' },
  ];

  // Geographic consent data
  const geographicData = [
    { region: 'Europe', consents: 45000, rate: 82.3 },
    { region: 'North America', consents: 38000, rate: 76.8 },
    { region: 'Asia Pacific', consents: 25000, rate: 71.2 },
    { region: 'Latin America', consents: 12000, rate: 68.5 },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          {/* <h1 className="text-3xl font-bold">Cookie & Consent Management</h1> */}
          <p className="text-gray-600">
            Comprehensive cookie tracking, consent management, and compliance automation
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics Report
          </Button>
          <Button variant="outline" className="gap-2">
            <Download className="h-4 w-4" />
            Cookie Report
          </Button>
          <Button className="gap-2 bg-custom-primary text-white hover:bg-custom-primary hover:text-white">
            <Settings className="h-4 w-4" />
            Banner Settings
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        {keyMetrics.map((metric, index) => {
          const Icon = metric.icon;
          return (
            <Card key={index} className="relative overflow-hidden">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="mb-1 text-sm font-medium text-gray-600">{metric.title}</p>
                  </div>
                  <div className={`rounded-lg p-3 ${metric.bgColor}`}>
                    {/* <Icon className={`h-6 w-6 ${metric.iconColor}`} /> */}
                  </div>
                </div>
                <div>
                  <p className="mb-2 text-3xl font-bold">{metric.value}</p>
                  {metric.subtitle && <p className="text-xs text-gray-500">{metric.subtitle}</p>}
                  {metric.showProgress && (
                    <div className="mt-3">
                      <Progress value={metric.progressValue} className="h-2" />
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Main Dashboard Tabs */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="cookies">Cookie Inventory</TabsTrigger>
          <TabsTrigger value="consent">Consent Banners</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="compliance">Compliance</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* Cookies by Category Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Cookies by Category</CardTitle>
                <CardDescription>
                  Distribution of cookies across different categories
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center">
                  <DonutWithText
                    chartConfig={cookieCategoryDataToUse?.config}
                    chartData={cookieCategoryDataToUse?.data}
                    chartSubtext="Risk Score"
                    totalValue={cookieCategoryData?.length}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Consent Interaction Trends */}
            <Card>
              <CardHeader>
                <CardTitle>Consent Interaction Trends</CardTitle>
                <CardDescription>Monthly consent acceptance and decline trends</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={consentTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="accepted"
                      stroke="#10b981"
                      strokeWidth={3}
                      name="Accepted"
                    />
                    <Line
                      type="monotone"
                      dataKey="declined"
                      stroke="#ef4444"
                      strokeWidth={3}
                      name="Declined"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Additional Metrics */}
          <Card>
            <CardHeader>
              <CardTitle>Additional Metrics</CardTitle>
              <CardDescription>Key performance indicators and trends</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-6 md:grid-cols-4">
                {additionalMetrics.map((metric, index) => (
                  <div key={index} className="text-center">
                    <div className="mb-1 text-2xl font-bold">{metric.value}</div>
                    <div className="mb-1 text-sm text-gray-600">{metric.label}</div>
                    <div className="text-xs text-green-600">{metric.trend}</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            <Card className="cursor-pointer transition-shadow hover:shadow-md">
              <CardContent className="p-6 text-center">
                <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100">
                  <Scan className="h-6 w-6 text-blue-600" />
                </div>
                <h3 className="mb-2 font-semibold">Cookie Scanner</h3>
                <p className="mb-4 text-sm text-gray-600">
                  Automatically discover and classify cookies across your website
                </p>
                <Button variant="outline" className="w-full">
                  Run Cookie Scan
                </Button>
              </CardContent>
            </Card>

            <Card className="cursor-pointer transition-shadow hover:shadow-md">
              <CardContent className="p-6 text-center">
                <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-green-100">
                  <Settings className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="mb-2 font-semibold">Consent Banner</h3>
                <p className="mb-4 text-sm text-gray-600">
                  Configure and customize your consent management banners
                </p>
                <Button variant="outline" className="w-full">
                  Configure Banner
                </Button>
              </CardContent>
            </Card>

            <Card className="cursor-pointer transition-shadow hover:shadow-md">
              <CardContent className="p-6 text-center">
                <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-lg bg-purple-100">
                  <BarChart3 className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="mb-2 font-semibold">Compliance Check</h3>
                <p className="mb-4 text-sm text-gray-600">
                  Review compliance status and generate audit reports
                </p>
                <Button variant="outline" className="w-full">
                  Run Compliance Check
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="cookies" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Cookie Inventory Management</CardTitle>
              <CardDescription>Manage and monitor all cookies across your domains</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex gap-2">
                    <Button variant="outline">Filter by Category</Button>
                    <Button variant="outline">Filter by Domain</Button>
                  </div>
                  <Button className="bg-custom-primary text-white hover:bg-custom-primary hover:text-white">
                    Add New Cookie
                  </Button>
                </div>
                <div className="py-12 text-center text-gray-500">
                  Cookie inventory interface will be displayed here
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="consent" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Consent Banner Management</CardTitle>
              <CardDescription>Configure and manage consent banners</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="py-12 text-center text-gray-500">
                Consent banner configuration interface will be displayed here
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* Geographic Consent Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Geographic Distribution</CardTitle>
                <CardDescription>Consent rates by geographic region</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {geographicData.map((region, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">{region.region}</div>
                        <div className="text-sm text-gray-600">
                          {region.consents.toLocaleString()} consents
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold">{region.rate}%</div>
                        <div className="mt-1 h-2 w-24 rounded-full bg-gray-200">
                          <div
                            className="h-2 rounded-full bg-blue-500"
                            style={{ width: `${region.rate}%` }}
                          ></div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Real-time Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Real-time Activity</CardTitle>
                <CardDescription>Live consent interactions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 rounded-lg bg-green-50 p-3">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <div className="flex-1">
                      <div className="text-sm font-medium">Consent accepted</div>
                      <div className="text-xs text-gray-600">Germany • 2 seconds ago</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 rounded-lg bg-blue-50 p-3">
                    <Eye className="h-4 w-4 text-blue-600" />
                    <div className="flex-1">
                      <div className="text-sm font-medium">Banner viewed</div>
                      <div className="text-xs text-gray-600">United States • 5 seconds ago</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 rounded-lg bg-orange-50 p-3">
                    <MousePointer className="h-4 w-4 text-orange-600" />
                    <div className="flex-1">
                      <div className="text-sm font-medium">Preferences updated</div>
                      <div className="text-xs text-gray-600">France • 8 seconds ago</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="compliance" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Compliance Status</CardTitle>
              <CardDescription>Regulatory compliance across different frameworks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {complianceMetrics.map((compliance, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between rounded-lg border p-4"
                  >
                    <div className="flex items-center space-x-4">
                      <Shield className="h-8 w-8 text-blue-600" />
                      <div>
                        <h4 className="font-medium">{compliance.framework}</h4>
                        <p className="text-sm text-gray-600">
                          Compliance Score: {compliance.score}%
                        </p>
                      </div>
                    </div>
                    <Badge variant={compliance.status === 'Compliant' ? 'default' : 'secondary'}>
                      {compliance.status}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Dashboard Settings</CardTitle>
              <CardDescription>Configure your dashboard preferences</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="py-12 text-center text-gray-500">
                Dashboard settings interface will be displayed here
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
