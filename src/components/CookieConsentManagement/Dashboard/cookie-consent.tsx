import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  TabsTrigger,
} from '../../../@/components/ui/Common/Elements/Tabs/Tabs';
import { ConsentManagement } from './cookie-consent-management';
import { CookieDashboard } from './cookie-dashboard';
import { CookieDiscovery } from './cookie-discovery';
import { BannerBuilder } from './cookiee-banner';
import { Analytics } from './cookies-analysis';
import { IntelligentTargeting } from './intelligent-targeting';

export default function CookieConsentDashboard() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-slate-900">Cookie Consent Management</h1>
          <p className="mt-2 text-slate-600">
            Enterprise-grade consent management with advanced customization and analytics
          </p>
        </div>
      </div>

      <Tabs defaultValue="dashboard" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="builder">Banner Builder</TabsTrigger>
          <TabsTrigger value="consent">Consent Management</TabsTrigger>
          <TabsTrigger value="discovery">Cookie Discovery</TabsTrigger>
          <TabsTrigger value="targeting">Smart Targeting</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard">
          <CookieDashboard />
        </TabsContent>
        <TabsContent value="builder">
          <BannerBuilder />
        </TabsContent>

        <TabsContent value="consent">
          <ConsentManagement />
        </TabsContent>

        <TabsContent value="discovery">
          <CookieDiscovery />
        </TabsContent>

        <TabsContent value="targeting">
          <IntelligentTargeting />
        </TabsContent>

        <TabsContent value="analytics">
          <Analytics />
        </TabsContent>
      </Tabs>
    </div>
  );
}
