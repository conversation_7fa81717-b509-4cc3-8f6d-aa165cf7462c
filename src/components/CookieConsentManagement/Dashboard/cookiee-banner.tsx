import { Eye, Monitor, Smartphone, Tablet } from 'lucide-react';
import { Badge } from '../../../@/components/ui/badge';
import { Button } from '../../../@/components/ui/Common/Elements/Button/Button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../@/components/ui/Common/Elements/Card/Card';
import { Input } from '../../../@/components/ui/Common/Elements/Input/Input';
import { Label } from '../../../@/components/ui/Common/Elements/Label/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../@/components/ui/Common/Elements/Select/Select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '../../../@/components/ui/Common/Elements/Tabs/Tabs';
import { Switch } from '../../../@/components/ui/switch';
import { Textarea } from '../../../@/components/ui/textarea';

export function BannerBuilder() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Visual Banner Builder</h2>
          <p className="text-slate-600">Create and customize your cookie consent banner</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Eye className="mr-2 h-4 w-4" />
            Preview
          </Button>
          <Button className="bg-custom-primary text-white hover:bg-custom-primary hover:text-white">
            Save Banner
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Builder Panel */}
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Banner Configuration</CardTitle>
              <CardDescription>Drag and drop components to build your banner</CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="content" className="w-full">
                <TabsList className="grid w-full grid-cols-4">
                  <TabsTrigger value="content">Content</TabsTrigger>
                  <TabsTrigger value="design">Design</TabsTrigger>
                  <TabsTrigger value="position">Position</TabsTrigger>
                  <TabsTrigger value="behavior">Behavior</TabsTrigger>
                </TabsList>

                <TabsContent value="content" className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">Banner Title</Label>
                    <Input id="title" placeholder="We value your privacy" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      placeholder="We use cookies to enhance your browsing experience..."
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="acceptText">Accept Button Text</Label>
                    <Input id="acceptText" placeholder="Accept All" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="rejectText">Reject Button Text</Label>
                    <Input id="rejectText" placeholder="Reject All" />
                  </div>
                </TabsContent>

                <TabsContent value="design" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Primary Color</Label>
                      <div className="flex space-x-2">
                        <Input type="color" value="#3b82f6" className="w-16" />
                        <Input value="#3b82f6" />
                      </div>
                    </div>
                    <div className="space-y-2">
                      <Label>Background Color</Label>
                      <div className="flex space-x-2">
                        <Input type="color" value="#ffffff" className="w-16" />
                        <Input value="#ffffff" />
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label>Font Family</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select font" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="inter">Inter</SelectItem>
                        <SelectItem value="roboto">Roboto</SelectItem>
                        <SelectItem value="arial">Arial</SelectItem>
                        <SelectItem value="helvetica">Helvetica</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Border Radius</Label>
                    <Input type="number" placeholder="8" />
                  </div>
                </TabsContent>

                <TabsContent value="position" className="space-y-4">
                  <div className="space-y-2">
                    <Label>Banner Position</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select position" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="top">Top</SelectItem>
                        <SelectItem value="bottom">Bottom</SelectItem>
                        <SelectItem value="overlay">Center Overlay</SelectItem>
                        <SelectItem value="corner-bottom-right">Bottom Right Corner</SelectItem>
                        <SelectItem value="corner-bottom-left">Bottom Left Corner</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="fixed" />
                    <Label htmlFor="fixed">Fixed Position</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="backdrop" />
                    <Label htmlFor="backdrop">Show Backdrop</Label>
                  </div>
                </TabsContent>

                <TabsContent value="behavior" className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Switch id="autoShow" />
                    <Label htmlFor="autoShow">Auto-show on page load</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Switch id="hideAfterAction" />
                    <Label htmlFor="hideAfterAction">Hide after user action</Label>
                  </div>
                  <div className="space-y-2">
                    <Label>Display Frequency</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="Select frequency" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="always">Always</SelectItem>
                        <SelectItem value="once">Once per session</SelectItem>
                        <SelectItem value="daily">Once per day</SelectItem>
                        <SelectItem value="weekly">Once per week</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </div>

        {/* Preview Panel */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Eye className="h-5 w-5" />
                <span>Live Preview</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Monitor className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Tablet className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Smartphone className="h-4 w-4" />
                  </Button>
                </div>
                <div className="min-h-[300px] rounded-lg border bg-slate-50 p-4">
                  <div className="rounded-lg border bg-white p-4 shadow-sm">
                    <h3 className="mb-2 font-semibold">We value your privacy</h3>
                    <p className="mb-4 text-sm text-slate-600">
                      We use cookies to enhance your browsing experience, serve personalized ads or
                      content, and analyze our traffic.
                    </p>
                    <div className="flex space-x-2">
                      <Button
                        size="sm"
                        className="bg-custom-primary text-white hover:bg-custom-primary hover:text-white"
                      >
                        Accept All
                      </Button>
                      <Button variant="outline" size="sm">
                        Reject All
                      </Button>
                      <Button variant="outline" size="sm">
                        Customize
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Templates</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  <Badge variant="secondary" className="mr-2">
                    GDPR
                  </Badge>
                  EU Compliance
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Badge variant="secondary" className="mr-2">
                    CCPA
                  </Badge>
                  California
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Badge variant="secondary" className="mr-2">
                    Basic
                  </Badge>
                  Simple Banner
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
