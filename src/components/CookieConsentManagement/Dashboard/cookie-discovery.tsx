import { Download, Globe, Plus, Scan, Search, Zap } from 'lucide-react';
import { Button } from '../../../@/components/ui/Common/Elements/Button/Button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../@/components/ui/Common/Elements/Card/Card';
import { Input } from '../../../@/components/ui/Common/Elements/Input/Input';
import { Progress } from '../../../@/components/ui/Common/Elements/ProgressBar/ProgressBar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../@/components/ui/Common/Elements/Select/Select';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '../../../@/components/ui/Common/Elements/Tabs/Tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../@/components/ui/Common/Table/Table';
import { Badge } from '../../../@/components/ui/badge';

export function CookieDiscovery() {
  const discoveredCookies = [
    {
      name: '_ga',
      domain: 'example.com',
      category: 'Analytics',
      vendor: 'Google Analytics',
      type: 'Third-party',
      expiration: '2 years',
      httpOnly: false,
      secure: true,
      purpose: 'Used to distinguish users',
      lastSeen: '2024-01-15',
      classification: 'Automatic',
    },
    {
      name: 'session_id',
      domain: 'example.com',
      category: 'Necessary',
      vendor: 'Internal',
      type: 'First-party',
      expiration: 'Session',
      httpOnly: true,
      secure: true,
      purpose: 'Session management',
      lastSeen: '2024-01-15',
      classification: 'Manual',
    },
    {
      name: '_fbp',
      domain: 'facebook.com',
      category: 'Marketing',
      vendor: 'Facebook',
      type: 'Third-party',
      expiration: '90 days',
      httpOnly: false,
      secure: true,
      purpose: 'Facebook pixel tracking',
      lastSeen: '2024-01-14',
      classification: 'AI-Powered',
    },
  ];

  const scanResults = [
    {
      domain: 'example.com',
      lastScan: '2024-01-15 10:30',
      status: 'Completed',
      cookiesFound: 42,
      newCookies: 3,
      changes: 1,
      progress: 100,
    },
    {
      domain: 'subdomain.example.com',
      lastScan: '2024-01-15 09:45',
      status: 'In Progress',
      cookiesFound: 28,
      newCookies: 0,
      changes: 0,
      progress: 75,
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Cookie Discovery & Management</h2>
          <p className="text-slate-600">Automated cookie scanning and intelligent classification</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button className="bg-custom-primary text-white hover:bg-custom-primary hover:text-white">
            <Scan className="mr-2 h-4 w-4" />
            Start Scan
          </Button>
        </div>
      </div>

      <Tabs defaultValue="discovered" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="discovered">Discovered Cookies</TabsTrigger>
          <TabsTrigger value="scanning">Automated Scanning</TabsTrigger>
          <TabsTrigger value="classification">Classification</TabsTrigger>
          <TabsTrigger value="domains">Domain Management</TabsTrigger>
        </TabsList>

        <TabsContent value="discovered" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Cookie Inventory</CardTitle>
              <CardDescription>All discovered cookies across your domains</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-6 flex space-x-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                    <Input placeholder="Search cookies..." className="pl-10" />
                  </div>
                </div>
                <Select>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter by category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="necessary">Necessary</SelectItem>
                    <SelectItem value="analytics">Analytics</SelectItem>
                    <SelectItem value="marketing">Marketing</SelectItem>
                    <SelectItem value="functional">Functional</SelectItem>
                  </SelectContent>
                </Select>
                <Select>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="first-party">First-party</SelectItem>
                    <SelectItem value="third-party">Third-party</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Cookie Name</TableHead>
                    <TableHead>Domain</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Vendor</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Expiration</TableHead>
                    <TableHead>Classification</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {discoveredCookies.map((cookie, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-mono">{cookie.name}</TableCell>
                      <TableCell>{cookie.domain}</TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            cookie.category === 'Necessary'
                              ? 'default'
                              : cookie.category === 'Analytics'
                                ? 'secondary'
                                : cookie.category === 'Marketing'
                                  ? 'destructive'
                                  : 'outline'
                          }
                        >
                          {cookie.category}
                        </Badge>
                      </TableCell>
                      <TableCell>{cookie.vendor}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{cookie.type}</Badge>
                      </TableCell>
                      <TableCell>{cookie.expiration}</TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          {cookie.classification === 'AI-Powered' && (
                            <Zap className="h-3 w-3 text-yellow-500" />
                          )}
                          <span className="text-sm">{cookie.classification}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Button variant="ghost" size="sm">
                          Edit
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="scanning" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Scan Schedule</CardTitle>
                <CardDescription>Configure automated cookie scanning</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div>
                    <h3 className="font-medium">Weekly Deep Scan</h3>
                    <p className="text-sm text-slate-600">Every Sunday at 2:00 AM</p>
                  </div>
                  <Badge variant="default">Active</Badge>
                </div>
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div>
                    <h3 className="font-medium">Daily Quick Scan</h3>
                    <p className="text-sm text-slate-600">Daily at 6:00 AM</p>
                  </div>
                  <Badge variant="secondary">Paused</Badge>
                </div>
                <Button className="w-full">
                  <Plus className="mr-2 h-4 w-4" />
                  Add New Schedule
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Scan Results</CardTitle>
                <CardDescription>Recent scanning activity</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {scanResults.map((result, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Globe className="h-4 w-4 text-slate-400" />
                          <span className="font-medium">{result.domain}</span>
                        </div>
                        <Badge variant={result.status === 'Completed' ? 'default' : 'secondary'}>
                          {result.status}
                        </Badge>
                      </div>
                      <Progress value={result.progress} className="h-2" />
                      <div className="flex justify-between text-sm text-slate-600">
                        <span>{result.cookiesFound} cookies found</span>
                        <span>{result.newCookies} new</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Advanced Scanning Options</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                <div className="space-y-4">
                  <h4 className="font-medium">Scan Depth</h4>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select depth" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="surface">Surface Level</SelectItem>
                      <SelectItem value="deep">Deep Scan</SelectItem>
                      <SelectItem value="comprehensive">Comprehensive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-4">
                  <h4 className="font-medium">Include Subdomains</h4>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Subdomain options" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">Main domain only</SelectItem>
                      <SelectItem value="direct">Direct subdomains</SelectItem>
                      <SelectItem value="all">All subdomains</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-4">
                  <h4 className="font-medium">AI Classification</h4>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="AI options" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="enabled">Enabled</SelectItem>
                      <SelectItem value="disabled">Manual only</SelectItem>
                      <SelectItem value="review">AI + Manual Review</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="classification" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Necessary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-600">12</div>
                <p className="text-sm text-slate-600">Essential cookies</p>
                <div className="mt-2">
                  <Badge variant="outline" className="text-xs">
                    100% Auto-classified
                  </Badge>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Analytics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-blue-600">8</div>
                <p className="text-sm text-slate-600">Performance tracking</p>
                <div className="mt-2">
                  <Badge variant="outline" className="text-xs">
                    85% Auto-classified
                  </Badge>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Marketing</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-purple-600">15</div>
                <p className="text-sm text-slate-600">Advertising cookies</p>
                <div className="mt-2">
                  <Badge variant="outline" className="text-xs">
                    92% Auto-classified
                  </Badge>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Functional</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-orange-600">5</div>
                <p className="text-sm text-slate-600">Feature enhancement</p>
                <div className="mt-2">
                  <Badge variant="outline" className="text-xs">
                    78% Auto-classified
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Classification Rules</CardTitle>
              <CardDescription>Configure automatic cookie classification rules</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div>
                    <h4 className="font-medium">Google Analytics Pattern</h4>
                    <p className="text-sm text-slate-600">
                      Auto-classify _ga*, _gid cookies as Analytics
                    </p>
                  </div>
                  <Badge variant="default">Active</Badge>
                </div>
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div>
                    <h4 className="font-medium">Session Cookie Rule</h4>
                    <p className="text-sm text-slate-600">HttpOnly session cookies as Necessary</p>
                  </div>
                  <Badge variant="default">Active</Badge>
                </div>
                <div className="flex items-center justify-between rounded-lg border p-4">
                  <div>
                    <h4 className="font-medium">Social Media Pixels</h4>
                    <p className="text-sm text-slate-600">Facebook, Twitter pixels as Marketing</p>
                  </div>
                  <Badge variant="secondary">Draft</Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="domains" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Domain Management</CardTitle>
              <CardDescription>Manage domains and multi-site cookie scanning</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="flex space-x-4">
                  <Input placeholder="Add new domain..." className="flex-1" />
                  <Button>Add Domain</Button>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center justify-between rounded-lg border p-4">
                    <div className="flex items-center space-x-4">
                      <Globe className="h-6 w-6 text-blue-600" />
                      <div>
                        <h4 className="font-medium">example.com</h4>
                        <p className="text-sm text-slate-600">Primary domain • 42 cookies</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="default">Verified</Badge>
                      <Button variant="outline" size="sm">
                        Configure
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between rounded-lg border p-4">
                    <div className="flex items-center space-x-4">
                      <Globe className="h-6 w-6 text-green-600" />
                      <div>
                        <h4 className="font-medium">subdomain.example.com</h4>
                        <p className="text-sm text-slate-600">Subdomain • 28 cookies</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="default">Verified</Badge>
                      <Button variant="outline" size="sm">
                        Configure
                      </Button>
                    </div>
                  </div>

                  <div className="flex items-center justify-between rounded-lg border p-4">
                    <div className="flex items-center space-x-4">
                      <Globe className="h-6 w-6 text-slate-400" />
                      <div>
                        <h4 className="font-medium">newdomain.com</h4>
                        <p className="text-sm text-slate-600">Pending verification</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary">Pending</Badge>
                      <Button variant="outline" size="sm">
                        Verify
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
