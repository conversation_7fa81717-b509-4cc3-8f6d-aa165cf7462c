import { Clock, Globe, Plus, Smartphone, Target } from 'lucide-react';
import { Button } from '../../../@/components/ui/Common/Elements/Button/Button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../../../@/components/ui/Common/Elements/Card/Card';
import { Input } from '../../../@/components/ui/Common/Elements/Input/Input';
import { Label } from '../../../@/components/ui/Common/Elements/Label/Label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../@/components/ui/Common/Elements/Select/Select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../@/components/ui/Common/Table/Table';
import { Badge } from '../../../@/components/ui/badge';
import { Switch } from '../../../@/components/ui/switch';

export function IntelligentTargeting() {
  const geoRules = [
    {
      id: 1,
      name: 'EU GDPR Compliance',
      region: 'European Union',
      regulation: 'GDPR',
      bannerType: 'Explicit Consent',
      status: 'Active',
      countries: 27,
    },
    {
      id: 2,
      name: 'California CCPA',
      region: 'California, USA',
      regulation: 'CCPA',
      bannerType: 'Do Not Sell',
      status: 'Active',
      countries: 1,
    },
    {
      id: 3,
      name: 'Brazil LGPD',
      region: 'Brazil',
      regulation: 'LGPD',
      bannerType: 'Consent Required',
      status: 'Draft',
      countries: 1,
    },
  ];

  const behavioralRules = [
    {
      name: 'New Visitor Welcome',
      trigger: 'First Visit',
      frequency: 'Once',
      device: 'All',
      timing: 'Immediate',
      status: 'Active',
    },
    {
      name: 'Mobile Optimized',
      trigger: 'Mobile Device',
      frequency: 'Per Session',
      device: 'Mobile',
      timing: '3 seconds delay',
      status: 'Active',
    },
    {
      name: 'Returning User Reminder',
      trigger: 'Return Visit > 30 days',
      frequency: 'Weekly',
      device: 'All',
      timing: 'Page scroll 50%',
      status: 'Active',
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Intelligent Targeting & Personalization</h2>
          <p className="text-slate-600">
            Configure geo-targeting and behavioral rules for consent banners
          </p>
        </div>
        <Button className="bg-custom-primary text-white hover:bg-custom-primary hover:text-white">
          <Plus className="mr-2 h-4 w-4" />
          Add Rule
        </Button>
      </div>

      {/* Geo-Targeting Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Globe className="h-5 w-5" />
            <span>Geo-Targeting & Compliance Rules</span>
          </CardTitle>
          <CardDescription>
            Automatically apply jurisdiction-specific banner configurations based on user location
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Rule Name</TableHead>
                <TableHead>Region</TableHead>
                <TableHead>Regulation</TableHead>
                <TableHead>Banner Type</TableHead>
                <TableHead>Countries</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {geoRules.map((rule) => (
                <TableRow key={rule.id}>
                  <TableCell className="font-medium">{rule.name}</TableCell>
                  <TableCell>{rule.region}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{rule.regulation}</Badge>
                  </TableCell>
                  <TableCell>{rule.bannerType}</TableCell>
                  <TableCell>
                    <Badge variant="secondary">{rule.countries} countries</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge variant={rule.status === 'Active' ? 'default' : 'secondary'}>
                      {rule.status}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <Button variant="outline" size="sm">
                      Configure
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Behavioral Targeting */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Target className="h-5 w-5" />
            <span>Behavioral Targeting Rules</span>
          </CardTitle>
          <CardDescription>
            Personalize banner display based on user behavior and device characteristics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Rule Name</TableHead>
                <TableHead>Trigger</TableHead>
                <TableHead>Frequency</TableHead>
                <TableHead>Device</TableHead>
                <TableHead>Timing</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {behavioralRules.map((rule, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{rule.name}</TableCell>
                  <TableCell>{rule.trigger}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{rule.frequency}</Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-1">
                      {rule.device === 'Mobile' && <Smartphone className="h-4 w-4" />}
                      <span>{rule.device}</span>
                    </div>
                  </TableCell>
                  <TableCell>{rule.timing}</TableCell>
                  <TableCell>
                    <Switch checked={rule.status === 'Active'} />
                  </TableCell>
                  <TableCell>
                    <Button variant="outline" size="sm">
                      Edit
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Advanced Configuration */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>VPN Detection & Handling</CardTitle>
            <CardDescription>Configure how to handle VPN traffic</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch id="vpnDetection" />
              <Label htmlFor="vpnDetection">Enable VPN detection</Label>
            </div>
            <div className="space-y-2">
              <Label>Fallback Configuration</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select fallback" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="strict">Apply strictest rule (GDPR)</SelectItem>
                  <SelectItem value="lenient">Apply most lenient rule</SelectItem>
                  <SelectItem value="default">Use default banner</SelectItem>
                  <SelectItem value="geoip">Best-guess from IP range</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="text-sm text-slate-600">
              Choose how to handle users with masked locations
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Performance Settings</CardTitle>
            <CardDescription>Optimize targeting performance</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Geolocation Cache TTL</Label>
              <div className="flex space-x-2">
                <Input type="number" placeholder="24" className="w-20" />
                <Badge variant="outline">hours</Badge>
              </div>
            </div>
            <div className="space-y-2">
              <Label>Rule Evaluation Timeout</Label>
              <div className="flex space-x-2">
                <Input type="number" placeholder="500" className="w-20" />
                <Badge variant="outline">ms</Badge>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Switch id="preload" />
              <Label htmlFor="preload">Preload targeting rules</Label>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Frequency Capping */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5" />
            <span>Frequency Capping & User Experience</span>
          </CardTitle>
          <CardDescription>Prevent banner fatigue and optimize user experience</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
            <div className="space-y-4">
              <h4 className="font-medium">Daily Limits</h4>
              <div className="space-y-2">
                <Label>Max displays per day</Label>
                <Input type="number" placeholder="3" />
              </div>
              <div className="space-y-2">
                <Label>Cooldown period</Label>
                <div className="flex space-x-2">
                  <Input type="number" placeholder="4" className="w-20" />
                  <Badge variant="outline">hours</Badge>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium">User Behavior</h4>
              <div className="flex items-center space-x-2">
                <Switch id="respectDismiss" />
                <Label htmlFor="respectDismiss">Respect dismiss action</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="trackEngagement" />
                <Label htmlFor="trackEngagement">Track engagement metrics</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="adaptiveFreq" />
                <Label htmlFor="adaptiveFreq">Adaptive frequency</Label>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium">Override Conditions</h4>
              <div className="flex items-center space-x-2">
                <Switch id="newConsent" />
                <Label htmlFor="newConsent">New consent categories</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="policyUpdate" />
                <Label htmlFor="policyUpdate">Policy updates</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="legalChange" />
                <Label htmlFor="legalChange">Legal requirement changes</Label>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
